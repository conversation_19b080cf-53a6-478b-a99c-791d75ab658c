<?php

namespace Tests\Unit;

use App\Services\TextEncodingService;
use PHPUnit\Framework\TestCase;

class TextEncodingServiceTest extends TestCase
{
    /**
     * Test conversion of common Danish character encoding issues
     */
    public function test_converts_danish_characters(): void
    {
        $testCases = [
            // Danish characters
            'Ã¸' => 'ø',
            'Ã¦' => 'æ',
            'Ã¥' => 'å',
            'Ã˜' => 'Ø',
            'Ã†' => 'Æ',
            'Ã…' => 'Å',

            // Product names from the examples
            'Jack - rundt spisebord Ã¸ 120 cm' => 'Jack - rundt spisebord ø 120 cm',
            'Ryan hÃ¸jt barbord i mangotrÃ¦ og metal' => '<PERSON> højt barbord i mangotræ og metal',
            'Botanic lerkrukke med hÃ¥ndtag' => 'Botanic lerkrukke med håndtag',
            'Spin - drejeskammel med trÃ¦sÃ¦de i genbrugstrÃ¦ og sort stel' => 'Spin - drejeskammel med træsæde i genbrugstræ og sort stel',
            'Seia -  Puf i Ã¦gte lÃ¦der' => 'Seia -  Puf i ægte læder',
            'Reed -  BÃ¦nk i Ã¦gte lÃ¦der og patineret metal' => 'Reed -  Bænk i ægte læder og patineret metal',
            'Hoskins -  BÃ¦nk i sort genbrugstrÃ¦' => 'Hoskins -  Bænk i sort genbrugstræ',
            'Zona -  Puf i lÃ¦der' => 'Zona -  Puf i læder',
            'Washington -  VÃ¦greol med fire rum i metal' => 'Washington -  Vægreol med fire rum i metal',
            '"Inca -  VÃ¦gskab i sort trÃ¦, 30 x 90 cm."' => '"Inca -  Vægskab i sort træ, 30 x 90 cm."',
            '"Inca - Skab i sort trÃ¦, 40 x 114 cm."' => '"Inca - Skab i sort træ, 40 x 114 cm."',
            '"Muir - Unikt trÃ¦fad, 59 x 75 cm."' => '"Muir - Unikt træfad, 59 x 75 cm."',
            '"Muir - Unikt trÃ¦fad, 25 x 90 cm."' => '"Muir - Unikt træfad, 25 x 90 cm."',
            'Mayor - Sofabord i teaktrÃ¦ - 50 x 50 cm.' => 'Mayor - Sofabord i teaktræ - 50 x 50 cm.',
            '"Oviedo -  Teak Spisebord, Ã¸ 70 cm."' => '"Oviedo -  Teak Spisebord, ø 70 cm."',
            'Michigan -  CafÃ©bord i non-wood og aluminium' => 'Michigan -  Cafébord i non-wood og aluminium',
            'Boca - HavebÃ¦nk i buet teaktrÃ¦' => 'Boca - Havebænk i buet teaktræ',
            'Cabo - HavebÃ¦nk i teaktrÃ¦' => 'Cabo - Havebænk i teaktræ',
            'Atlantic -  Havestol af dÃ¦kstolstypen i teak' => 'Atlantic -  Havestol af dækstolstypen i teak',
            '"Meno - TÃ¦ppe i sandfarvet genbrugsplast, 200 x 300 cm."' => '"Meno - Tæppe i sandfarvet genbrugsplast, 200 x 300 cm."',
            '"Meno - TÃ¦ppe i sandfarvet genbrugsplast, Ã¸ 120 cm"' => '"Meno - Tæppe i sandfarvet genbrugsplast, ø 120 cm"',
            '"Meno - TÃ¦ppe i sandfarvet genbrugsplast, 140 x 200 cm."' => '"Meno - Tæppe i sandfarvet genbrugsplast, 140 x 200 cm."',
            '"Azur - Lampe med vinkÃ¸ler, genopladelig"' => '"Azur - Lampe med vinkøler, genopladelig"',
            'Luz - Lanterne i stÃ¥l og glas - sÃ¦t med 2 stk.' => 'Luz - Lanterne i stål og glas - sæt med 2 stk.',
            '"Violin figentrÃ¦ - Kunstig plante, hÃ¸jde 190 cm."' => '"Violin figentræ - Kunstig plante, højde 190 cm."',
            '"OliventrÃ¦ - Kunstig plante, hÃ¸jde 120 cm."' => '"Oliventræ - Kunstig plante, højde 120 cm."',
            '"OliventrÃ¦ - Kunstig plante, hÃ¸jde 150 cm."' => '"Oliventræ - Kunstig plante, højde 150 cm."',
            '"Skimmia TrÃ¦ - Kunstig plante, hÃ¸jde 75 cm."' => '"Skimmia Træ - Kunstig plante, højde 75 cm."',
            '"Violinfigen trÃ¦ - Kunstig plante, hÃ¸jde 100 cm."' => '"Violinfigen træ - Kunstig plante, højde 100 cm."',
            '"FigentrÃ¦ - Kunstig plante, hÃ¸jde 50 cm."' => '"Figentræ - Kunstig plante, højde 50 cm."',
            '"MagnoliatrÃ¦ - Kunstig plante, hÃ¸jde 75 cm."' => '"Magnoliatræ - Kunstig plante, højde 75 cm."',
            '"Sorghum GrÃ¦s - Kunstig plante, hÃ¸jde 110 cm."' => '"Sorghum Græs - Kunstig plante, højde 110 cm."',
            '"Banff - Plaid i mÃ¸rkegrÃ¥-hvidt mÃ¸nster, 130 x 160 cm."' => '"Banff - Plaid i mørkegrå-hvidt mønster, 130 x 160 cm."',
            '"Banff - Plaid i grÃ¸nt og hvidt mÃ¸nster, 130 x 160 cm."' => '"Banff - Plaid i grønt og hvidt mønster, 130 x 160 cm."',
            '"Nabu - Plaid i flere farver i akryl, 130 x 170 cm."' => '"Nabu - Plaid i flere farver i akryl, 130 x 170 cm."',
            '"Arucas -  Pude i brunt stof, 45 x 60 cm."' => '"Arucas -  Pude i brunt stof, 45 x 60 cm."',
            '"Arucas - Pude i grÃ¥brunt stof, 45 x 60 cm."' => '"Arucas - Pude i gråbrunt stof, 45 x 60 cm."',
        ];

        foreach ($testCases as $input => $expected) {
            $result = TextEncodingService::cleanProductText($input);
            $this->assertEquals($expected, $result, "Failed to convert: '{$input}' to '{$expected}', got: '{$result}'");
        }
    }

    /**
     * Test that already correct UTF-8 text is not changed
     */
    public function test_preserves_correct_utf8(): void
    {
        $correctTexts = [
            'Book-Keeper - 2 Hylder i sort metal',
            'Thomas skrivebord med skuffer i metal',
            'Bella - metal café bord med en rå finish',
            'San Francisco - firkantet træbord',
            'Jumper lille plint/skammel - sort',
            'Colmar lerkrukke',
            'Marley reol i genbrugstræ og metal',
            'Patna - Spisebord i antiksort træ',
            'Randa - Konsolbord med metaltop',
            'Kofel - Sofabord i metal',
        ];

        foreach ($correctTexts as $text) {
            $result = TextEncodingService::cleanProductText($text);
            $this->assertEquals($text, $result, "Incorrectly modified correct UTF-8 text: '{$text}'");
        }
    }

    /**
     * Test that empty and null values are handled correctly
     */
    public function test_handles_empty_values(): void
    {
        $this->assertEquals('', TextEncodingService::cleanProductText(''));
        $this->assertEquals('', TextEncodingService::cleanProductText(null));
        $this->assertEquals('', TextEncodingService::cleanProductText('   '));
    }

    /**
     * Test that whitespace is normalized
     */
    public function test_normalizes_whitespace(): void
    {
        $testCases = [
            '  Multiple   spaces   ' => 'Multiple spaces',
            "Line\nbreaks\tand\ttabs" => 'Line breaks and tabs',
            '   Leading and trailing   ' => 'Leading and trailing',
        ];

        foreach ($testCases as $input => $expected) {
            $result = TextEncodingService::cleanProductText($input);
            $this->assertEquals($expected, $result, "Failed to normalize whitespace: '{$input}'");
        }
    }

    /**
     * Test UTF-8 validation
     */
    public function test_utf8_validation(): void
    {
        $this->assertTrue(TextEncodingService::isValidUtf8('Valid UTF-8 text with æøå'));
        $this->assertTrue(TextEncodingService::isValidUtf8('English text'));
        $this->assertTrue(TextEncodingService::isValidUtf8(''));

        // Test with problematic encoding
        $this->assertFalse(TextEncodingService::isValidUtf8("Invalid \xFF\xFE UTF-8"));
    }
}
