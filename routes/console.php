<?php

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Inspiring;
use Illuminate\Support\Facades\Artisan;

Artisan::command('inspire', function () {
    $this->comment(Inspiring::quote());
})->purpose('Display an inspiring quote');

Artisan::command('merchants:update', function () {
    $this->call('merchants:update');
})->describe('Update network merchants from their sources');

// Schedule the command to run hourly
app(Schedule::class)->command('merchants:update')->hourly();
