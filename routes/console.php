<?php

use App\Console\Commands\MerchantsLogosSync;
use App\Console\Commands\MerchantsProductsHardDelete;
use App\Console\Commands\MerchantsProductsSync;
use App\Console\Commands\MerchantsReviewsSync;
use App\Console\Commands\MerchantsSync;
use Illuminate\Support\Facades\Schedule;

// Cron - Merchant sync commands
Schedule::command(MerchantsSync::class)->dailyAt('23:00');
Schedule::command(MerchantsProductsSync::class)->dailyAt('23:30');
Schedule::command(MerchantsReviewsSync::class)->weeklyOn(0, '22:00');
Schedule::command(MerchantsLogosSync::class)->weeklyOn(0, '22:30');

// Cron - Maintenance commands
Schedule::command(MerchantsProductsHardDelete::class)->monthlyOn(1, '02:00');

// Queue management
Schedule::command('queue:retry all')
    ->dailyAt('02:01');

Schedule::command('queue:work --stop-when-empty --timeout=0 --memory=2048')->everyFiveMinutes();
