<?php

use App\Models\MerchantProduct;
use App\Models\Product;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

// # ROUTE TO GET USER

Route::get('/user', function (Request $request) {
    return $request->user();
})->middleware('auth:sanctum');

// # ROUTE TO GET PRODUCTS

Route::get('/products', function () {
    // Fetch all products along with their associated MerchantProducts
    $products = Product::with('merchantProducts')->get();

    // Map the products to include only the related MerchantProduct data
    $merchantProducts = $products->flatMap(function ($product) {
        // `flatMap` will extract all merchantProducts from each product and merge them into a single collection
        return $product->merchantProducts;
    });

    // Return the merchantProducts as JSON response
    return response()->json($merchantProducts);
})->middleware('auth:sanctum');

// # ROUTE TO GET PRODUCTS ON SPECIFIC EAN

Route::get('/products/{ean}', function ($ean) {
    // Find the product by EAN
    $product = Product::where('ean', $ean)->first();

    if ($product) {
        // Load all related MerchantProducts and their Merchant information
        $merchantProducts = $product->merchantProducts()->with('merchant')->get();

        if ($merchantProducts->isNotEmpty()) {
            return response()->json([
                'product' => $product, // TODO: Unsure if we need this?
                'merchants' => $merchantProducts->map(function ($merchantProduct) {
                    return [
                        'merchant_name' => $merchantProduct->merchant->name, // Include the merchant name
                        'merchant_product' => $merchantProduct, // Include the rest of the merchant product details
                    ];
                }),
            ]);
        } else {
            return response()->json(['message' => 'No merchant products found for this EAN'], 404);
        }
    } else {
        return response()->json(['message' => 'Product not found'], 404);
    }
})->middleware('auth:sanctum');
