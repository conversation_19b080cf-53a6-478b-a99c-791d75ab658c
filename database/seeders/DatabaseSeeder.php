<?php

use App\Models\Company;
use App\Models\NetworkAccount;
use App\Models\NetworkCompany;
use App\Models\User;
use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    public function run()
    {
        $user = User::create([
            'name' => 'Kasper',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'is_admin' => 1,
        ]);

        $company = Company::create([
            'name' => 'Kasper Stück',
            'domain' => 'kasperstuck.dk',
            'slug' => 'kasperstuck-dk',
            'country' => 'da',
            'currency' => 'DKK',
        ]);

        // Forbind bruger og firma
        $company->users()->attach($user->id);

        NetworkCompany::create([
            'name' => 'PartnerAds',
            'country' => 'DK',
            'url' => 'https://www.partner-ads.com/dk/programoversigt_xml.php?godkendte=1&key=',
        ]);
        NetworkCompany::create([
            'name' => 'PartnerAds',
            'country' => 'NO',
            'url' => 'https://www.partner-ads.com/no/programoversigt_xml.php?godkendte=1&key=',
        ]);
        NetworkCompany::create([
            'name' => 'PartnerAds',
            'country' => 'SE',
            'url' => 'https://www.partner-ads.com/se/programoversigt_xml.php?godkendte=1&key=',
        ]);
        NetworkCompany::create([
            'name' => 'Adtraction',
            'country' => 'DK',
            'url' => 'https://api.adtraction.com/v3/partner/programs/?token=',
        ]);

        NetworkAccount::create([
            'network_company_id' => 1,
            'company_id' => $company->id,
            'name' => 'Partner Ads - Denmark',
            'country' => 'DK',
            'password' => '22877138609708041931',
        ]);

        NetworkAccount::create([
            'network_company_id' => 2,
            'company_id' => $company->id,
            'name' => 'Partner Ads - Norway',
            'country' => 'NO',
            'password' => '22877138609708041931',
        ]);

        NetworkAccount::create([
            'network_company_id' => 3,
            'company_id' => $company->id,
            'name' => 'Partner Ads - Norway',
            'country' => 'SE',
            'password' => '22877138609708041931',
        ]);

        NetworkAccount::create([
            'network_company_id' => 4,
            'company_id' => $company->id,
            'name' => 'Adtraction',
            'country' => 'DK',
            'password' => '6D0371B566DF1D6F6AC8079CE77E94F85D5E7EA7',
            'channel_id' => '**********',
        ]);

        NetworkAccount::create([
            'network_company_id' => 4,
            'company_id' => $company->id,
            'name' => 'Adtraction2',
            'country' => 'DK',
            'password' => '6D0371B566DF1D6F6AC8079CE77E94F85D5E7EA7',
            'channel_id' => '**********',
        ]);

    }
}
