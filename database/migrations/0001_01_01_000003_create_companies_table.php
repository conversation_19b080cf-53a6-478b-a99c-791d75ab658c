<?php

use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('companies', function (Blueprint $table) {
            $table->id();

            $table->string('name');
            $table->string('domain');
            $table->string('slug')->unique();

            $table->string('country', 2);
            $table->string('currency', 3);

            $table->string('logo')->nullable();
            $table->longText('description')->nullable();

            $table->string('niche')->nullable();
            $table->string('goals')->nullable();
            $table->string('audience')->nullable(); // audience specific needs, preferences, and behaviors
            $table->string('guidelines')->nullable(); // text tone, style

            $table->longText('mission')->nullable();
            $table->longText('values')->nullable();

            $table->timestamps();
        });

        Schema::create('company_user', function (Blueprint $table) {
            $table->id();

            $table->foreignIdFor(Company::class)->constrained();
            $table->foreignIdFor(User::class)->constrained();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('companies');
        Schema::dropIfExists('user_company');
    }
};
