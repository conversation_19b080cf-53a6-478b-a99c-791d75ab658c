<?php

use App\Models\Merchant;
use App\Models\NetworkCompany;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('merchant_feed_urls', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Merchant::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(NetworkCompany::class)->constrained()->cascadeOnDelete();
            $table->string('feed_url', 500)->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('merchant_feed_urls');
    }
};
