<?php

use App\Models\Merchant;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('merchant_products', function (Blueprint $table) {
            $table->id();
            $table->string('name', 500);
            $table->string('ean')->nullable();
            $table->string('unique_identifier')->unique()->nullable();
            $table->foreignIdFor(Merchant::class)->constrained()->cascadeOnDelete();
            $table->string('sku')->nullable();

            $table->string('brand')->nullable();
            $table->string('category', 500)->nullable();
            $table->longText('description')->nullable();

            $table->float('price')->nullable();
            $table->float('old_price')->nullable();

            $table->string('shipping')->nullable();
            $table->float('shipping_price')->nullable();

            $table->string('image_url');
            $table->string('direct_url', 1000)->nullable();
            $table->string('tracking_url', 2000)->nullable();

            $table->softDeletes();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('merchant_products');
    }
};
