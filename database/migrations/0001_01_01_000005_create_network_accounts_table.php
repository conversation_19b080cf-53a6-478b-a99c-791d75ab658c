<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('network_accounts', function (Blueprint $table) {

            $table->id();
            $table->foreignId('company_id')->constrained()->onDelete('cascade');
            $table->foreignId('network_company_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string(column: 'user')->nullable();
            $table->string('password', 500);
            $table->string('channel_id')->nullable();
            $table->string('country', 2);

            $table->text('last_error_message')->nullable();
            $table->timestamp('last_error_time')->nullable();

            $table->timestamps();

        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('network_accounts');
    }
};
