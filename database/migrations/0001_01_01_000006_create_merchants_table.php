<?php

use App\Models\NetworkAccount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('merchants', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('unique_identifier')->unique();

            $table->foreignIdFor(NetworkAccount::class)->constrained()->cascadeOnDelete();
            $table->string('program_id');

            $table->string('merchant_email')->nullable();
            $table->string('merchant_contact')->nullable();

            $table->string('merchant_url', 500)->nullable();
            $table->string('domain');

            $table->string('currency', 3)->nullable();
            $table->json('merchant_conditions')->nullable();

            $table->float('click_rate')->nullable();
            $table->float('lead_rate')->nullable();
            $table->float('provision')->nullable();
            $table->float('epc')->nullable();

            $table->boolean('allow_sem_ppc')->default(false);
            $table->boolean('allow_shopping_ads')->default(false);
            $table->boolean('allow_social_ppc')->default(false);
            $table->boolean('allow_cashback')->default(false);
            $table->boolean('allow_discount_sites')->default(false);

            $table->boolean('feed')->nullable();
            $table->timestamp('feed_updated')->nullable();

            $table->text('last_error_message')->nullable();
            $table->timestamp('last_error_time')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Add indexes for frequently queried columns
            $table->index('network_account_id');
            $table->index('program_id');
            $table->index('feed_updated');
            $table->index(['name', 'merchant_url']);
            $table->index('unique_identifier');
            $table->index('domain');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('merchants');
    }
};
