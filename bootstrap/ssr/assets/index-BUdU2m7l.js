import { jsxs, Fragment, jsx } from "react/jsx-runtime";
import { use<PERSON><PERSON>, <PERSON>, <PERSON> } from "@inertiajs/react";
function Welcome() {
  const { auth } = usePage().props;
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsxs(Head, { children: [
      /* @__PURE__ */ jsx("title", { children: "Your page title" }),
      /* @__PURE__ */ jsx("meta", { name: "description", content: "Your page description" })
    ] }),
    /* @__PURE__ */ jsx("div", { children: "Hallo" }),
    /* @__PURE__ */ jsx(Link, { href: "/dashboard", children: "Test" })
  ] });
}
export {
  Welcome as default
};
