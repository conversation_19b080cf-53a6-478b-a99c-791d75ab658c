version: '3.8'

# Laravel deployment with Nginx Unit

services:
    # Main application service with Nginx Unit
    app:
        build:
            context: .
            dockerfile: Dockerfile
        container_name: app
        working_dir: /var/www/html
        ports:
            - "8080"
        environment:
            - APP_ENV=production
            - APP_DEBUG=false
            - APP_KEY=${APP_KEY}
            - DB_CONNECTION=${DB_CONNECTION:-mysql}
            - DB_HOST=${DB_HOST:-mysql}
            - DB_PORT=${DB_PORT:-3306}
            - DB_DATABASE=${DB_DATABASE:-laravel}
            - DB_USERNAME=${DB_USERNAME:-root}
            - DB_PASSWORD=${DB_PASSWORD:-}
            - REDIS_HOST=${REDIS_HOST:-redis}
            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
            - REDIS_PORT=${REDIS_PORT:-6379}
        healthcheck:
            test: [ "CMD", "healthcheck-app" ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        restart: unless-stopped
        networks:
            - app-network

    # Inertia SSR service
    inertia-ssr:
        build:
            context: .
            dockerfile: Dockerfile
        command: [ "php", "/var/www/html/artisan", "inertia:start-ssr" ]
        stop_signal: SIGTERM
        healthcheck:
            test: [ "CMD", "healthcheck-inertia-ssr" ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        restart: unless-stopped
        depends_on:
            - app
        networks:
            - app-network
        environment:
            - APP_ENV=production
            - APP_DEBUG=false
            - APP_KEY=${APP_KEY}
            - DB_CONNECTION=${DB_CONNECTION:-mysql}
            - DB_HOST=${DB_HOST:-mysql}
            - DB_PORT=${DB_PORT:-3306}
            - DB_DATABASE=${DB_DATABASE:-laravel}
            - DB_USERNAME=${DB_USERNAME:-root}
            - DB_PASSWORD=${DB_PASSWORD:-}
            - REDIS_HOST=${REDIS_HOST:-redis}
            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
            - REDIS_PORT=${REDIS_PORT:-6379}

    # Scheduler service
    schedule:
        build:
            context: .
            dockerfile: Dockerfile
        command: [ "php", "/var/www/html/artisan", "schedule:work" ]
        stop_signal: SIGTERM
        healthcheck:
            test: [ "CMD", "healthcheck-schedule" ]
            interval: 30s
            timeout: 10s
            retries: 3
            start_period: 30s
        restart: unless-stopped
        depends_on:
            - app
        networks:
            - app-network
        environment:
            - APP_ENV=production
            - APP_DEBUG=false
            - APP_KEY=${APP_KEY}
            - DB_CONNECTION=${DB_CONNECTION:-mysql}
            - DB_HOST=${DB_HOST:-mysql}
            - DB_PORT=${DB_PORT:-3306}
            - DB_DATABASE=${DB_DATABASE:-laravel}
            - DB_USERNAME=${DB_USERNAME:-root}
            - DB_PASSWORD=${DB_PASSWORD:-}
            - REDIS_HOST=${REDIS_HOST:-redis}
            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
            - REDIS_PORT=${REDIS_PORT:-6379}

    # Queue worker service
#    queue:
#        build:
#            context: .
#            dockerfile: Dockerfile
#        command: [ "php", "/var/www/html/artisan", "queue:work", "--sleep=3", "--tries=3", "--max-time=3600" ]
#        stop_signal: SIGTERM
#        healthcheck:
#            test: [ "CMD", "healthcheck-queue" ]
#            interval: 30s
#            timeout: 10s
#            retries: 3
#            start_period: 10s
#        restart: unless-stopped
#        depends_on:
#            - app
#        networks:
#            - app-network
#        environment:
#            - APP_ENV=production
#            - APP_DEBUG=false
#            - APP_KEY=${APP_KEY}
#            - DB_CONNECTION=${DB_CONNECTION:-mysql}
#            - DB_HOST=${DB_HOST:-mysql}
#            - DB_PORT=${DB_PORT:-3306}
#            - DB_DATABASE=${DB_DATABASE:-laravel}
#            - DB_USERNAME=${DB_USERNAME:-root}
#            - DB_PASSWORD=${DB_PASSWORD:-}
#            - REDIS_HOST=${REDIS_HOST:-redis}
#            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
#            - REDIS_PORT=${REDIS_PORT:-6379}

    # Horizon worker service
#    horizon:
#        build:
#            context: .
#            dockerfile: Dockerfile
#        command: [ "php", "/var/www/html/artisan", "horizon" ]
#        stop_signal: SIGTERM
#        healthcheck:
#            test: [ "CMD", "healthcheck-horizon" ]
#            interval: 30s
#            timeout: 10s
#            retries: 3
#            start_period: 10s
#        restart: unless-stopped
#        depends_on:
#            - app
#        networks:
#            - app-network
#        environment:
#            - APP_ENV=production
#            - APP_DEBUG=false
#            - APP_KEY=${APP_KEY}
#            - DB_CONNECTION=${DB_CONNECTION:-mysql}
#            - DB_HOST=${DB_HOST:-mysql}
#            - DB_PORT=${DB_PORT:-3306}
#            - DB_DATABASE=${DB_DATABASE:-laravel}
#            - DB_USERNAME=${DB_USERNAME:-root}
#            - DB_PASSWORD=${DB_PASSWORD:-}
#            - REDIS_HOST=${REDIS_HOST:-redis}
#            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
#            - REDIS_PORT=${REDIS_PORT:-6379}

    # Pulse Check service
#    pulse_check:
#        build:
#            context: .
#            dockerfile: Dockerfile
#        command: [ "php", "/var/www/html/artisan", "pulse:check" ]
#        stop_signal: SIGTERM
#        healthcheck:
#            test: [ "CMD", "healthcheck-pulse" ]
#            interval: 30s
#            timeout: 10s
#            retries: 3
#            start_period: 10s
#        restart: unless-stopped
#        depends_on:
#            - app
#        networks:
#            - app-network
#        environment:
#            - APP_ENV=production
#            - APP_DEBUG=false
#            - APP_KEY=${APP_KEY}
#            - DB_CONNECTION=${DB_CONNECTION:-mysql}
#            - DB_HOST=${DB_HOST:-mysql}
#            - DB_PORT=${DB_PORT:-3306}
#            - DB_DATABASE=${DB_DATABASE:-laravel}
#            - DB_USERNAME=${DB_USERNAME:-root}
#            - DB_PASSWORD=${DB_PASSWORD:-}
#            - REDIS_HOST=${REDIS_HOST:-redis}
#            - REDIS_PASSWORD=${REDIS_PASSWORD:-null}
#            - REDIS_PORT=${REDIS_PORT:-6379}

networks:
    app-network:
        driver: bridge
