<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class MerchantProduct extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'name',
        'ean',
        'merchant_id',
        'unique_identifier',
        'sku',
        'brand',
        'category',
        'description',
        'shipping',
        'shipping_price',
        'price',
        'old_price',
        'image_url',
        'direct_url',
        'tracking_url',
    ];

    protected $casts = [
        'price' => 'float',
        'old_price' => 'float',
        'shipping_price' => 'float',
        'deleted_at' => 'datetime',
    ];

    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('active', function ($query) {
            $query->whereNull('deleted_at');
        });
    }

    public function network(): BelongsTo
    {
        return $this->belongsTo(Network::class);
    }

    public function merchant(): BelongsTo
    {
        return $this->belongsTo(Merchant::class);
    }

    public function prices(): HasMany
    {
        return $this->hasMany(MerchantProductPrice::class);
    }

    /**
     * Mutator for name field to ensure proper UTF-8 encoding
     */
    public function setNameAttribute($value): void
    {
        $this->attributes['name'] = \App\Services\TextEncodingService::cleanProductText($value);
    }

    /**
     * Mutator for description field to ensure proper UTF-8 encoding
     */
    public function setDescriptionAttribute($value): void
    {
        $this->attributes['description'] = \App\Services\TextEncodingService::cleanProductText($value);
    }

    /**
     * Mutator for brand field to ensure proper UTF-8 encoding
     */
    public function setBrandAttribute($value): void
    {
        $this->attributes['brand'] = \App\Services\TextEncodingService::cleanProductText($value);
    }

    /**
     * Mutator for category field to ensure proper UTF-8 encoding
     */
    public function setCategoryAttribute($value): void
    {
        $this->attributes['category'] = \App\Services\TextEncodingService::cleanProductText($value);
    }

    /**
     * Mutator for shipping field to ensure proper UTF-8 encoding
     */
    public function setShippingAttribute($value): void
    {
        $this->attributes['shipping'] = \App\Services\TextEncodingService::cleanProductText($value);
    }
}
