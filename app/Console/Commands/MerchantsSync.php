<?php

namespace App\Console\Commands;

use App\Console\Traits\BenchmarksArtisanCommand;
use App\Models\Merchant;
use App\Models\NetworkAccount;
use App\Services\Http\HttpClient;
use App\Services\NetworkService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class MerchantsSync extends Command
{
    use BenchmarksArtisanCommand;

    protected $signature = 'command:merchants-sync';

    protected $description = 'Sync merchants from network sources';

    public function __construct()
    {
        parent::__construct();
    }

    public function handle(): void
    {
        $this->info(now()->format('H:i:s').' Starting merchants sync...');
        $this->startBenchmark();

        try {
            // Get all network accounts grouped by network company
            $networkAccounts = NetworkAccount::with('networkCompany')->get();
            $networkAccountsByCompany = $networkAccounts->groupBy('network_company_id');
            $allUpdatedProgramIds = [];

            // Process each network company's accounts
            foreach ($networkAccountsByCompany as $networkCompanyId => $companyAccounts) {
                // Check if any account in this company has a valid URL
                $hasValidUrl = $companyAccounts->some(function ($account) {
                    return $account->networkCompany && ! empty($account->networkCompany->url);
                });

                if (! $hasValidUrl) {
                    $this->warn(now()->format('H:i:s')." No valid URL found for network company ID: {$networkCompanyId}. Skipping all accounts for this company.");

                    continue;
                }

                $this->info(now()->format('H:i:s')." Processing network company ID: {$networkCompanyId} with ".$companyAccounts->count().' accounts');
                $companyProcessed = false;

                // Process each account for this network company
                foreach ($companyAccounts as $networkAccount) {
                    $networkService = new NetworkService(new HttpClient);
                    $networkService->initializeService($networkAccount);

                    try {
                        $this->info(now()->format('H:i:s')." Processing network account: {$networkAccount->name}");

                        if (! $networkAccount->networkCompany || empty($networkAccount->networkCompany->url)) {
                            $this->warn(now()->format('H:i:s')." No URL found for network account ID: {$networkAccount->id}");

                            continue;
                        }

                        $updatedProgramIds = [];
                        $this->info(now()->format('H:i:s')." Starting merchant stream for network: {$networkAccount->name}");

                        // Process merchants with timeout
                        $startTime = time();
                        $processedCount = 0;

                        try {
                            // Set a timeout alarm
                            set_time_limit(180 + 30); // 3 minutes + buffer time

                            // Process merchants and get the program IDs of updated merchants
                            $processedCount = $this->processWithTimeout($networkService, $networkAccount, 180); // 3 minutes
                        } catch (Exception $e) {
                            throw $e;
                        }

                        // Get all program IDs for this network account that were updated
                        if ($processedCount > 0) {
                            $updatedProgramIds = Merchant::where('network_account_id', $networkAccount->id)
                                ->where('updated_at', '>=', now()->subMinutes(30))
                                ->pluck('program_id')
                                ->toArray();

                            $this->info(now()->format('H:i:s')." Found {$processedCount} merchants with ".count($updatedProgramIds).' program IDs');
                        }

                        $allUpdatedProgramIds[$networkAccount->id] = $updatedProgramIds;
                        $this->info(now()->format('H:i:s')." Completed merchant stream for network: {$networkAccount->name}");

                        // Mark that at least one account in this company was processed successfully
                        $companyProcessed = true;

                        // Clear any previous error
                        if ($networkAccount->last_error_message) {
                            $networkAccount->last_error_message = null;
                            $networkAccount->last_error_time = null;
                            $networkAccount->save();
                        }
                    } catch (Exception $e) {
                        $this->error(now()->format('H:i:s')." Error processing network account {$networkAccount->name}: ".$e->getMessage());

                        // Record error in network account
                        $networkAccount->last_error_message = $e->getMessage();
                        $networkAccount->last_error_time = now();
                        $networkAccount->save();
                    }
                }

                // If all accounts for this company failed, log a warning
                if (! $companyProcessed) {
                    $this->warn(now()->format('H:i:s')." All network accounts for company ID {$networkCompanyId} failed to process.");
                }
            }

            if (empty($allUpdatedProgramIds)) {
                $this->warn(now()->format('H:i:s').' No merchants were updated for any network.');
            }

            $this->info(now()->format('H:i:s').' Deleting outdated merchants for all networks.');
            $this->cleanupOutdatedMerchants($allUpdatedProgramIds);

        } catch (Exception $e) {
            $this->error(now()->format('H:i:s').' Merchants sync failed: '.$e->getMessage());
            $this->error('Stack trace: '.$e->getTraceAsString());
            throw $e;
        } finally {
            $this->info(now()->format('H:i:s').' Merchants sync completed.');
            $this->endBenchmark();
        }
    }

    /**
     * Process merchants with a timeout
     */
    private function processWithTimeout(NetworkService $networkService, NetworkAccount $networkAccount, int $timeout): int
    {
        $startTime = time();
        $processedCount = 0;

        // Create a separate process to handle the processing
        try {
            // Start processing
            $processedCount = $networkService->processMerchants($networkAccount);

            // Check if we've exceeded the timeout
            if (time() - $startTime > $timeout) {
                $this->warn(now()->format('H:i:s')." Processing for network account {$networkAccount->name} exceeded time limit of {$timeout} seconds but completed.");
            }

            return $processedCount;
        } catch (Exception $e) {
            $elapsedTime = time() - $startTime;

            if ($elapsedTime >= $timeout) {
                $message = "Processing timed out after {$elapsedTime} seconds";
                Log::warning($message, [
                    'network_account_id' => $networkAccount->id,
                    'network_account_name' => $networkAccount->name,
                    'timeout' => $timeout,
                ]);
                throw new Exception($message);
            }

            // Re-throw the original exception
            throw $e;
        }
    }

    private function cleanupOutdatedMerchants(array $allUpdatedProgramIds): void
    {
        if (empty($allUpdatedProgramIds)) {
            $this->warn(now()->format('H:i:s').' Skipping cleanup as no merchants were updated.');

            return;
        }

        $this->info(now()->format('H:i:s').' Starting cleanup of outdated merchants.');
        foreach ($allUpdatedProgramIds as $networkId => $updatedProgramIds) {
            $this->deleteOutdatedMerchants($networkId, $updatedProgramIds);
        }
    }

    private function deleteOutdatedMerchants(int $networkId, array $updatedProgramIds): void
    {
        $count = Merchant::where('network_account_id', $networkId)
            ->whereNotIn('program_id', $updatedProgramIds)
            ->where('updated_at', '<', now()->subDays(3))
            ->count();

        if ($count > 0) {
            $this->info(now()->format('H:i:s')." Deleting {$count} outdated merchants for network ID: {$networkId}");

            Merchant::where('network_account_id', $networkId)
                ->whereNotIn('program_id', $updatedProgramIds)
                ->where('updated_at', '<', now()->subDays(3))
                ->delete();
        }
    }
}
