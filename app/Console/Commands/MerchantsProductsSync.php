<?php

namespace App\Console\Commands;

use App\Console\Traits\BenchmarksArtisanCommand;
use App\Models\MerchantFeedUrl;
use App\Models\MerchantProduct;
use App\Models\Product;
use App\Services\Http\HttpClient;
use App\Services\NetworkService;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Process\Pool;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Process;

/**
 * Command to sync merchant products from feeds and create unified products.
 *
 * This command:
 * 1. Syncs merchant products from feeds
 * 2. Creates unified product records from merchant products
 * 3. Groups products by EAN number
 * 4. Manages product relationships with merchant products
 * 5. Handles cleanup of outdated products
 *
 * Usage:
 * - php artisan command:merchants-products-sync (process all merchants)
 * - php artisan command:merchants-products-sync {merchant_id} (process specific merchant)
 * - php artisan command:merchants-products-sync --products-only (only sync products, skip merchant products)
 */
class MerchantsProductsSync extends Command
{
    use BenchmarksArtisanCommand;

    protected $signature = 'command:merchants-products-sync 
                            {merchant_id? : Optional merchant ID to process} 
                            {--products-only : Only sync products, skip merchant products}';

    protected $description = 'Sync merchant products from feeds and create unified products';

    /** @var array Stores processed EAN numbers for cleanup */
    private array $processedEans = [];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Main command handler.
     */
    public function handle(): void
    {
        $this->info('Starting merchant products sync...');
        $this->startBenchmark();

        try {
            // If products-only flag is set, skip merchant products sync
            if ($this->option('products-only')) {
                $this->syncProducts();
            } else {
                $this->syncMerchantProducts();

                // After syncing merchant products, also sync unified products
                $this->syncProducts();
            }
        } catch (Exception $e) {
            $this->error('Sync failed: '.$e->getMessage());
            throw $e;
        } finally {
            $this->info('Sync completed');
            $this->endBenchmark();
        }
    }

    /**
     * Sync merchant products from feeds.
     */
    private function syncMerchantProducts(): void
    {
        $this->info('Starting merchant products sync...');

        $merchantId = $this->argument('merchant_id');

        if ($merchantId) {
            $feedUrls = MerchantFeedUrl::with(['merchant', 'merchant.networkAccount.networkCompany'])
                ->where('merchant_id', $merchantId)
                ->get();

            if ($feedUrls->isEmpty()) {
                $this->warn("No feed URLs found for merchant ID: {$merchantId}");

                return;
            }

            foreach ($feedUrls as $feedUrl) {
                $merchant = $feedUrl->merchant;

                if (! $merchant) {
                    $this->error("Error processing Feed URL ID {$feedUrl->id} for Merchant ID {$merchantId}: Merchant data is missing or could not be loaded.");
                    Log::error("Merchant object is null for MerchantFeedUrl ID: {$feedUrl->id}, Merchant ID: {$merchantId}");

                    continue; // Skip to the next feed URL
                }

                // At this point, $merchant should be a valid object.
                $merchantNameForLogging = $merchant->name ?? "Unknown Merchant (ID: {$merchantId})";
                $this->info("Processing Merchant: {$merchantNameForLogging} - Feed URL ID: {$feedUrl->id}");

                try {
                    $networkService = new NetworkService(new HttpClient);
                    $networkService->processProducts($merchant, $feedUrl->feed_url);
                } catch (\Exception $e) {
                    $errorMessage = "Error processing merchant {$merchantNameForLogging} (Feed URL ID: {$feedUrl->id}): ".$e->getMessage().' in '.$e->getFile().' on line '.$e->getLine();
                    $this->error($errorMessage);
                    Log::error("Detailed error for merchant {$merchantNameForLogging}:", [
                        'merchant_id' => $merchant->id ?? $merchantId,
                        'merchant_name' => $merchantNameForLogging,
                        'feed_url_id' => $feedUrl->id,
                        'feed_url' => $feedUrl->feed_url,
                        'exception_message' => $e->getMessage(),
                        'exception_class' => get_class($e),
                        'exception_file' => $e->getFile(),
                        'exception_line' => $e->getLine(),
                        'exception_trace' => $e->getTraceAsString(), // Log the full stack trace
                    ]);
                }
            }
        } else {
            $merchantIds = MerchantFeedUrl::with(['merchant', 'merchant.networkAccount.networkCompany'])
                ->get()
                ->groupBy('merchant_id');

            if ($merchantIds->isEmpty()) {
                $this->warn('No feed URLs found');

                return;
            }

            // Process in chunks of 2
            foreach ($merchantIds->chunk(2) as $chunk) {
                $this->info('Processing chunk of '.$chunk->count().' merchants...');

                $pool = Process::pool(function (Pool $pool) use ($chunk) {
                    foreach ($chunk as $merchantId => $feedUrls) {
                        $pool->as("merchant_{$merchantId}")
                            ->command([
                                PHP_BINARY,
                                base_path('artisan'),
                                'command:merchants-products-sync',
                                $merchantId,
                            ]);
                    }
                })->start(function (string $type, string $output, string $key) {
                    if ($type === 'out') {
                        $this->info("[$key] $output");
                    } else {
                        $this->error("[$key] $output");
                    }
                });

                // Wait for all processes to finish
                $results = $pool->wait();

                // Check results
                foreach ($results as $key => $result) {
                    if (! $result->successful()) {
                        $this->error("Process $key failed: ".$result->errorOutput());
                    }
                }
            }
        }

        $this->info('Merchant products sync completed');
    }

    /**
     * Sync unified products from merchant products.
     *
     * Process flow:
     * 1. Process merchant products in chunks
     * 2. Create/update unified product records
     * 3. Clean up outdated products
     */
    private function syncProducts(): void
    {
        $this->info('Starting unified products sync...');

        try {
            MerchantProduct::chunk(1000, function ($merchantProducts) {
                foreach ($merchantProducts as $merchantProduct) {
                    $this->processMerchantProduct($merchantProduct);
                }
            });

            $this->cleanupOutdatedProducts();
        } catch (Exception $e) {
            $this->error('Products sync failed: '.$e->getMessage());
            throw $e;
        }

        $this->info('Unified products sync completed');
    }

    /**
     * Process individual merchant product.
     *
     * Creates or updates a unified product record based on
     * merchant product data if it has a valid EAN.
     */
    private function processMerchantProduct(MerchantProduct $merchantProduct): void
    {
        $ean = $merchantProduct->ean;

        if (! $this->isValidEan($ean)) {
            return;
        }

        Product::updateOrCreate(
            ['ean' => $ean],
            [
                'name' => $merchantProduct->name,
                'merchant_product_id' => $merchantProduct->id,
            ]
        );

        $this->processedEans[] = $ean;
    }

    /**
     * Validate EAN number format.
     *
     * Valid EAN numbers:
     * - Must be numeric
     * - Length between 8 and 14 digits
     *
     * @param  string|null  $ean  EAN number to validate
     * @return bool True if EAN is valid
     */
    private function isValidEan(?string $ean): bool
    {
        if (! $ean) {
            return false;
        }

        return preg_match('/^\d{8,14}$/', $ean) === 1;
    }

    /**
     * Remove products that no longer have associated merchant products.
     *
     * Deletes products that:
     * 1. Were not processed in current sync
     * 2. Haven't been updated in last 3 days
     */
    private function cleanupOutdatedProducts(): void
    {
        Product::whereNotIn('ean', $this->processedEans)
            ->where('updated_at', '<', Carbon::now()->subDays(3))
            ->delete();
    }
}
