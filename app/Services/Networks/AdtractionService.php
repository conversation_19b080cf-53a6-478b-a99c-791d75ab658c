<?php

namespace App\Services\Networks;

use App\Models\Merchant;
use App\Models\MerchantProduct;
use App\Models\NetworkAccount;
use App\Services\Http\HttpClient;
use Exception;
use Illuminate\Support\Facades\Log;

class AdtractionService
{
    private const DEFAULT_HEADERS = [
        'Accept' => 'application/json',
        'Content-Type' => 'application/json',
    ];

    private HttpClient $httpClient;

    public function __construct(
    ) {
        $this->httpClient = new HttpClient;
    }

    public function processMerchants(NetworkAccount $networkAccount): int
    {
        $timestamp = $this->getTimestamp();

        // Set url
        $url = $this->buildAuth($networkAccount);

        Log::info("[{$timestamp}] Starting Adtraction merchant stream", [
            'network_id' => $networkAccount->id,
            'url' => $url,
            'market' => $networkAccount->country,
            'channel_id' => $networkAccount->channel_id,
        ]);

        try {
            $buffer = '';
            $processedCount = 0;
            $merchantQueue = [];
            $domainNames = []; // Store domain names for batch processing
            $merchantDomainMap = []; // Map merchant IDs to domain names

            $this->httpClient->stream(
                'POST',
                $url,
                self::DEFAULT_HEADERS,
                [
                    'market' => $networkAccount->country,
                    'channelId' => $networkAccount->channel_id,
                ],
                function ($chunk) use (
                    &$buffer,
                    &$processedCount,
                    &$merchantQueue,
                    &$domainNames,
                    &$merchantDomainMap,
                    $timestamp,
                    $networkAccount
                ) {
                    $buffer .= $chunk;

                    // Try to decode JSON data
                    $data = json_decode($buffer);
                    if (json_last_error() === JSON_ERROR_NONE) {
                        $buffer = '';  // Clear buffer after successful decode

                        // First pass: collect all domain names
                        foreach ($data as $program) {
                            $merchantUrl = $program->programURL;
                            $parsedUrl = parse_url($merchantUrl, PHP_URL_HOST);

                            if ($parsedUrl) {
                                // Remove www. prefix if present
                                $domainName = preg_replace('/^www\./', '', $parsedUrl);
                                $domainNames[$domainName] = $domainName; // Use as key to avoid duplicates

                                // Store the mapping of merchant ID to domain name
                                $merchantId = $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$program->programId;
                                $merchantDomainMap[$merchantId] = $domainName;
                            }
                        }

                        // Batch process domains if we have enough
                        if (count($domainNames) >= 100) {
                            $this->processDomainBatch($domainNames);
                            $domainNames = []; // Reset after processing
                        }

                        foreach ($data as $program) {
                            try {
                                // Extract feed URLs if available
                                $feedUrls = [];
                                if (! empty($program->feeds)) {
                                    foreach ($program->feeds as $feed) {
                                        $feedUrls[] = [
                                            'feed_url' => $feed->feedUrl,
                                        ];
                                    }
                                }

                                // Get domain name from the map
                                $merchantId = $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$program->programId;
                                $domainName = $merchantDomainMap[$merchantId] ?? null;

                                $mappedData = [
                                    'unique_identifier' => $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$program->programId,
                                    'name' => $program->programName,
                                    'program_id' => (string) $program->programId,
                                    'merchant_email' => null,
                                    'merchant_contact' => null,
                                    'merchant_url' => $program->programURL,
                                    'domain' => $domainName,
                                    'currency' => $program->currency ?? 'DKK',
                                    'merchant_conditions' => null,
                                    'click_rate' => 0.00,
                                    'lead_rate' => 0.00,
                                    'provision' => 0.00,
                                    'epc' => (float) ($program->epc ?? 0.00),
                                    'allow_sem_ppc' => (bool) ($program->ppcMarketing ?? 0) === 1,
                                    'allow_shopping_ads' => false,
                                    'allow_social_ppc' => (bool) ($program->socialMarketing ?? 0) === 1,
                                    'allow_cashback' => (bool) ($program->cashbackMarketing ?? 0) === 1,
                                    'allow_discount_sites' => (bool) ($program->couponMarketing ?? 0) === 1,
                                    'feed' => ! empty($feedUrls),
                                    'network_account_id' => $networkAccount->id,
                                ];

                                // Create/update merchant
                                $merchant = Merchant::updateOrCreate(
                                    ['unique_identifier' => $mappedData['unique_identifier']],
                                    $mappedData
                                );

                                // Create feed URLs if available
                                if (! empty($feedUrls)) {
                                    foreach ($feedUrls as $feedUrl) {
                                        $merchant->feedUrls()->updateOrCreate(
                                            ['feed_url' => $feedUrl['feed_url']],
                                            [
                                                'network_company_id' => $networkAccount->network_company_id,
                                            ]
                                        );
                                    }
                                }

                                $processedCount++;

                                // Batch process when queue reaches 1000 merchants
                                if ($processedCount % 1000 == 0) {
                                    Log::info("[{$timestamp}] Processed {$processedCount} merchants so far");
                                }

                            } catch (Exception $e) {
                                Log::error("[{$timestamp}] Error processing merchant data", [
                                    'program' => $program,
                                    'error' => $e->getMessage(),
                                    'network_id' => $networkAccount->id,
                                    'trace' => $e->getTraceAsString(),
                                ]);
                            }
                        }

                        // Process remaining merchants in queue
                        if (! empty($merchantQueue)) {
                            Merchant::upsert(
                                $merchantQueue,
                                ['unique_identifier'],
                                array_keys(current($merchantQueue))
                            );
                            Log::info("[{$timestamp}] Processed {$processedCount} merchants so far");
                            $merchantQueue = [];
                        }
                    }
                }
            );

            // Process any remaining domains
            if (! empty($domainNames)) {
                $this->processDomainBatch($domainNames);
            }

            Log::info("[{$timestamp}] Completed processing {$processedCount} merchants");

            return $processedCount;

        } catch (Exception $e) {
            Log::error("[{$timestamp}] Adtraction stream failed: ".$e->getMessage(), [
                'network_id' => $networkAccount->id,
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * Process a batch of domains efficiently
     *
     * @param  array  $domainNames  Array of domain names to process
     */
    private function processDomainBatch(array $domainNames): void
    {
        if (empty($domainNames)) {
            return;
        }

        $timestamp = now()->format('Y-m-d H:i:s');

        try {
            // Get existing domains to avoid trying to create duplicates
            $existingDomains = \App\Models\Domain::whereIn('name', array_values($domainNames))->get();
            $existingDomainNames = $existingDomains->pluck('name')->toArray();

            // Find domains that need to be created
            $domainsToCreate = array_diff($domainNames, $existingDomainNames);

            if (! empty($domainsToCreate)) {
                $records = [];
                $now = now();

                foreach ($domainsToCreate as $domainName) {
                    $records[] = [
                        'name' => $domainName,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                // Batch insert new domains
                if (! empty($records)) {
                    \App\Models\Domain::insert($records);
                    Log::info("[{$timestamp}] Batch created ".count($records).' domains');
                }
            }
        } catch (Exception $e) {
            Log::error("[{$timestamp}] Error batch processing domains: ".$e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    private function buildAuth($networkAccount): string
    {
        $url = rtrim($networkAccount->networkCompany->url, '/');

        return $url.$networkAccount->password;
    }

    /**
     * Process products for a specific merchant using a given feed URL.
     * Fetches, parses, and stores product data from Adtraction JSON feeds.
     *
     * @param  object  $merchant  The merchant object (App\Models\Merchant).
     * @param  string  $feedUrl  The URL of the product feed to process.
     * @return int Number of products processed.
     */
    public function processProducts(Merchant $merchant, string $feedUrl): int
    {
        $timestamp = $this->getTimestamp();
        Log::info("[{$timestamp}] Starting Adtraction product processing for merchant: {$merchant->name} from feed: {$feedUrl}");
        $processedCount = 0;

        try {
            // For Adtraction feeds, we'll implement a basic version that processes a limited number of products
            // to avoid memory issues with very large feeds
            $maxProducts = 1000; // Limit to 1000 products to avoid memory issues

            Log::info("[{$timestamp}] Processing Adtraction feed with limit of {$maxProducts} products");

            // Try to get a partial feed content with a reasonable timeout
            $feedContent = $this->httpClient->get($feedUrl);

            if (empty($feedContent)) {
                Log::warning("[{$timestamp}] Empty Adtraction feed content for merchant: {$merchant->name}, URL: {$feedUrl}");

                return 0;
            }

            // Check feed size - if it's too large, skip processing
            $feedSize = strlen($feedContent);
            $maxFeedSize = 50 * 1024 * 1024; // 50MB limit

            if ($feedSize > $maxFeedSize) {
                Log::warning("[{$timestamp}] Adtraction feed too large ({$feedSize} bytes) for merchant: {$merchant->name}, skipping");

                return 0;
            }

            Log::info("[{$timestamp}] Processing Adtraction feed ({$feedSize} bytes) for merchant: {$merchant->name}");

            // Parse XML content
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($feedContent);

            if ($xml === false) {
                $errors = libxml_get_errors();
                Log::error("[{$timestamp}] Failed to parse XML from Adtraction feed: {$feedUrl}", [
                    'xml_errors' => array_map(function ($error) {
                        return $error->message;
                    }, $errors),
                ]);
                libxml_clear_errors();

                return 0;
            }

            // Find products in the XML structure
            $products = [];

            // Try different common XML structures for product feeds
            if (isset($xml->channel->item)) {
                // RSS-style feed
                $products = $xml->channel->item;
            } elseif (isset($xml->item)) {
                // Direct items
                $products = $xml->item;
            } elseif (isset($xml->product)) {
                // Direct products
                $products = $xml->product;
            } elseif (isset($xml->products->product)) {
                // Products wrapper
                $products = $xml->products->product;
            } else {
                Log::warning("[{$timestamp}] Unknown XML structure in Adtraction feed: {$feedUrl}");

                return 0;
            }

            if (empty($products)) {
                Log::warning("[{$timestamp}] No products found in Adtraction feed: {$feedUrl}");

                return 0;
            }

            $totalProducts = count($products);
            $productsToProcess = min($totalProducts, $maxProducts);

            Log::info("[{$timestamp}] Found {$totalProducts} products, processing {$productsToProcess}");

            $productCount = 0;
            foreach ($products as $product) {
                if ($productCount >= $maxProducts) {
                    break;
                }

                $productCount++;

                // Convert SimpleXML to array
                $productData = json_decode(json_encode($product), true);

                if (empty($productData)) {
                    continue;
                }

                try {
                    MerchantProduct::updateOrCreate(
                        [
                            'merchant_id' => $merchant->id,
                            'unique_identifier' => $merchant->id.'-'.($this->safeStringValue($productData['sku'] ?? $productData['productId'] ?? $productData['id'] ?? uniqid())),
                        ],
                        [
                            'name' => $this->safeStringValue($productData['title'] ?? $productData['name'] ?? $productData['productName'] ?? null),
                            'ean' => $this->safeStringValue($productData['ean'] ?? $productData['gtin'] ?? $productData['barcode'] ?? null),
                            'description' => $this->safeStringValue($productData['description'] ?? $productData['desc'] ?? null),
                            'price' => (float) ($productData['price'] ?? $productData['salePrice'] ?? $productData['currentPrice'] ?? 0.00),
                            'old_price' => (float) ($productData['regularPrice'] ?? $productData['originalPrice'] ?? $productData['listPrice'] ?? null),
                            'direct_url' => $this->safeStringValue($productData['link'] ?? $productData['productUrl'] ?? $productData['trackingUrl'] ?? $productData['url'] ?? null),
                            'image_url' => $this->safeStringValue($productData['image'] ?? $productData['imageUrl'] ?? $productData['picture'] ?? null),
                            'brand' => $this->safeStringValue($productData['brand'] ?? $productData['manufacturer'] ?? null),
                            'category' => $this->safeStringValue($productData['category'] ?? $productData['categoryName'] ?? $productData['productCategory'] ?? null),
                        ]
                    );
                    $processedCount++;

                    // Log progress every 100 products for smaller batches
                    if ($processedCount % 100 === 0) {
                        Log::info("[{$timestamp}] Processed {$processedCount} products so far for merchant: {$merchant->name}");
                    }

                } catch (\Exception $e) {
                    Log::error("[{$timestamp}] Error processing Adtraction product for merchant: {$merchant->name}", [
                        'product_data' => $productData,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info("[{$timestamp}] Successfully processed {$processedCount} Adtraction products for merchant: {$merchant->name}");

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error("[{$timestamp}] HTTP request failed for Adtraction product feed: {$feedUrl} for merchant: {$merchant->name}", [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? (string) $e->getResponse()->getBody() : null,
            ]);
        } catch (\Exception $e) {
            Log::error("[{$timestamp}] Failed to process Adtraction products for merchant: {$merchant->name} from {$feedUrl}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }

        return $processedCount;
    }

    /**
     * Safely convert value to string, handling arrays from XML conversion and UTF-8 encoding
     */
    private function safeStringValue($value): string
    {
        if (is_array($value)) {
            $stringValue = is_array($value) && empty($value) ? '' : (string) (is_array($value) ? ($value[0] ?? '') : $value);
        } else {
            $stringValue = (string) ($value ?? '');
        }

        // Convert to proper UTF-8 encoding
        return \App\Services\TextEncodingService::cleanProductText($stringValue);
    }

    private function getTimestamp(): string
    {
        return now()->format('H:i:s'); // Example format
    }
}
