<?php

namespace App\Services\Networks;

use App\Models\Merchant;
use App\Models\MerchantProduct;
use App\Models\NetworkAccount;
use App\Services\Http\HttpClient;
use Exception;
use Illuminate\Support\Facades\Log;

class PartnerAdsService
{
    private const DEFAULT_HEADERS = [
        'Accept' => 'application/xml',
        'Content-Type' => 'application/xml',
    ];

    // No rate limit settings - removed wait setup

    private HttpClient $httpClient;

    public function __construct(HttpClient $httpClient)
    {
        $this->httpClient = $httpClient;
    }

    public function processMerchants(NetworkAccount $networkAccount): int
    {
        $timestamp = now()->format('Y-m-d H:i:s');

        // Set url
        $url = $this->buildUrl($networkAccount);

        Log::info("[{$timestamp}] Starting PartnerAds merchant stream", [
            'network_id' => $networkAccount->id,
            'url' => $url,
        ]);

        try {
            $buffer = '';
            $processedCount = 0;
            $errorCount = 0;
            $merchantQueue = [];
            $domainNames = []; // Store domain names for batch processing
            $merchantDomainMap = []; // Map merchant IDs to domain names

            // Use streaming approach like Adtraction
            $this->httpClient->stream(
                'GET',
                $url,
                self::DEFAULT_HEADERS,
                [],
                function ($chunk) use (
                    &$buffer,
                    &$processedCount,
                    &$errorCount,
                    &$merchantQueue,
                    &$domainNames,
                    &$merchantDomainMap,
                    $timestamp,
                    $networkAccount
                ) {
                    $buffer .= $chunk;

                    // Process the buffer when it gets large enough
                    if (strlen($buffer) > 10240) { // Process after ~10KB of data
                        try {
                            // Parse the current buffer directly as XML
                            libxml_use_internal_errors(true);
                            $xml = simplexml_load_string($buffer);
                            if ($xml === false) {
                                throw new Exception('Incomplete XML data');
                            }

                            // Check if we have any of the expected structures
                            if (! isset($xml->program) &&
                                (! isset($xml->partnerprogrammer) || ! isset($xml->partnerprogrammer->program)) &&
                                (! isset($xml->partnerprogram) || ! isset($xml->partnerprogram->program))) {
                                throw new Exception('XML does not contain expected program data');
                            }

                            // Use the parseMerchants method to extract merchant data
                            $parsedData = $this->parseMerchants($buffer, $networkAccount->id);
                            $merchants = $parsedData['merchants'] ?? [];

                            // First pass: collect all domain names
                            foreach ($merchants as $merchantData) {
                                $merchantUrl = $merchantData['merchant_url'];
                                $parsedUrl = parse_url($merchantUrl, PHP_URL_HOST);

                                if ($parsedUrl) {
                                    // Remove www. prefix if present
                                    $domainName = preg_replace('/^www\./', '', $parsedUrl);
                                    $domainNames[$domainName] = $domainName; // Use as key to avoid duplicates

                                    // Store the mapping of merchant ID to domain name
                                    $uniqueIdentifier = $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$merchantData['program_id'];
                                    $merchantDomainMap[$uniqueIdentifier] = $domainName;
                                }
                            }

                            // Batch process domains if we have enough
                            if (count($domainNames) >= 100) {
                                $this->processDomainBatch($domainNames, $timestamp);
                                $domainNames = []; // Reset after processing
                            }

                            foreach ($merchants as $merchantData) {
                                try {
                                    // Create unique identifier
                                    $uniqueIdentifier = $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$merchantData['program_id'];

                                    // Get domain name from the map
                                    $domainName = $merchantDomainMap[$uniqueIdentifier] ?? null;

                                    $mappedData = [
                                        'unique_identifier' => $uniqueIdentifier,
                                        'name' => $merchantData['name'],
                                        'program_id' => $merchantData['program_id'],
                                        'merchant_url' => $merchantData['merchant_url'],
                                        'domain' => $domainName,
                                        'currency' => $merchantData['currency'] ?? 'DKK',
                                        'feed' => $merchantData['feed'] ?? false,
                                        'network_account_id' => $networkAccount->id,
                                    ];

                                    // Create/update merchant
                                    $merchant = Merchant::updateOrCreate(
                                        ['unique_identifier' => $mappedData['unique_identifier']],
                                        $mappedData
                                    );

                                    // Create feed URLs if available
                                    if ($mappedData['feed'] && isset($merchantData['feedUrls'])) {
                                        foreach ($merchantData['feedUrls'] as $feedUrl) {
                                            $merchant->feedUrls()->updateOrCreate(
                                                ['feed_url' => $feedUrl['feed_url']],
                                                [
                                                    'network_company_id' => $networkAccount->network_company_id,
                                                ]
                                            );
                                        }
                                    }

                                    $processedCount++;

                                    // Log progress periodically
                                    if ($processedCount % 100 == 0) {
                                        Log::info("[{$timestamp}] Processed {$processedCount} merchants so far");
                                    }
                                } catch (Exception $e) {
                                    $errorCount++;
                                    Log::error("[{$timestamp}] Error processing merchant data", [
                                        'merchant' => $merchantData,
                                        'error' => $e->getMessage(),
                                        'network_id' => $networkAccount->id,
                                        'trace' => $e->getTraceAsString(),
                                    ]);
                                }
                            }

                            // Clear the buffer after processing
                            $buffer = '';
                        } catch (Exception $e) {
                            // If we can't parse the current buffer, keep accumulating data
                            Log::warning("[{$timestamp}] Could not parse current buffer, continuing to accumulate data", [
                                'error' => $e->getMessage(),
                                'buffer_length' => strlen($buffer),
                            ]);
                        }
                    }
                }
            );

            // Process any remaining data in the buffer
            if (! empty($buffer)) {
                try {
                    // Parse the remaining buffer directly as XML
                    libxml_use_internal_errors(true);
                    $xml = simplexml_load_string($buffer);
                    if ($xml === false) {
                        throw new Exception('Invalid XML data in final buffer');
                    }

                    // Check if we have any of the expected structures
                    if (! isset($xml->program) &&
                        (! isset($xml->partnerprogrammer) || ! isset($xml->partnerprogrammer->program)) &&
                        (! isset($xml->partnerprogram) || ! isset($xml->partnerprogram->program))) {
                        throw new Exception('XML does not contain expected program data');
                    }

                    // Use the parseMerchants method to extract merchant data
                    $parsedData = $this->parseMerchants($buffer, $networkAccount->id);
                    $merchants = $parsedData['merchants'] ?? [];

                    foreach ($merchants as $merchantData) {
                        try {
                            // Create unique identifier
                            $uniqueIdentifier = $networkAccount->network_company_id.'-'.$networkAccount->id.'-'.$merchantData['program_id'];

                            $mappedData = [
                                'unique_identifier' => $uniqueIdentifier,
                                'name' => $merchantData['name'],
                                'program_id' => $merchantData['program_id'],
                                'merchant_url' => $merchantData['merchant_url'],
                                'logo_url' => $merchantData['logo_url'],
                                'currency' => $merchantData['currency'] ?? 'DKK',
                                'feed' => $merchantData['feed'] ?? false,
                                'network_account_id' => $networkAccount->id,
                            ];

                            // Create/update merchant
                            $merchant = Merchant::updateOrCreate(
                                ['unique_identifier' => $mappedData['unique_identifier']],
                                $mappedData
                            );

                            // Create feed URLs if available
                            if ($mappedData['feed'] && isset($merchantData['feedUrls'])) {
                                foreach ($merchantData['feedUrls'] as $feedUrl) {
                                    $merchant->feedUrls()->updateOrCreate(
                                        ['feed_url' => $feedUrl['feed_url']],
                                        [
                                            'network_company_id' => $networkAccount->network_company_id,
                                        ]
                                    );
                                }
                            }

                            $processedCount++;
                        } catch (Exception $e) {
                            $errorCount++;
                            Log::error("[{$timestamp}] Error processing merchant data", [
                                'merchant' => $merchantData,
                                'error' => $e->getMessage(),
                                'network_id' => $networkAccount->id,
                                'trace' => $e->getTraceAsString(),
                            ]);
                        }
                    }
                } catch (Exception $e) {
                    Log::warning("[{$timestamp}] Could not parse final buffer", [
                        'error' => $e->getMessage(),
                        'buffer_length' => strlen($buffer),
                    ]);
                }
            }

            // Process any remaining domains
            if (! empty($domainNames)) {
                $this->processDomainBatch($domainNames, $timestamp);
            }

            Log::info("[{$timestamp}] Finished PartnerAds merchant stream. Total processed: {$processedCount}, Errors: {$errorCount}", [
                'network_id' => $networkAccount->id,
            ]);

            return $processedCount;

        } catch (Exception $e) {
            Log::error("[{$timestamp}] Critical error during PartnerAds merchant stream", [
                'network_id' => $networkAccount->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return $processedCount;
        }
    }

    /**
     * Process a batch of domains efficiently
     *
     * @param  array  $domainNames  Array of domain names to process
     * @param  string  $timestamp  Current timestamp for logging
     */
    private function processDomainBatch(array $domainNames, string $timestamp): void
    {
        if (empty($domainNames)) {
            return;
        }

        try {
            // Get existing domains to avoid trying to create duplicates
            $existingDomains = \App\Models\Domain::whereIn('name', array_values($domainNames))->get();
            $existingDomainNames = $existingDomains->pluck('name')->toArray();

            // Find domains that need to be created
            $domainsToCreate = array_diff($domainNames, $existingDomainNames);

            if (! empty($domainsToCreate)) {
                $records = [];
                $now = now();

                foreach ($domainsToCreate as $domainName) {
                    $records[] = [
                        'name' => $domainName,
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                // Batch insert new domains
                if (! empty($records)) {
                    \App\Models\Domain::insert($records);
                    Log::info("[{$timestamp}] Batch created ".count($records).' domains');
                }
            }
        } catch (Exception $e) {
            Log::error("[{$timestamp}] Error batch processing domains: ".$e->getMessage(), [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }

    private function buildUrl(NetworkAccount $networkAccount): string
    {
        // Partner Ads URLs should end with the API key directly
        // The URL format should be: https://www.partner-ads.com/dk/programoversigt_xml.php?key=YOUR_API_KEY

        $url = rtrim($networkAccount->networkCompany->url, '/');
        $password = trim($networkAccount->password);

        // Log for debugging
        Log::info('Building URL for Partner Ads', [
            'original_url' => $url,
            'network_id' => $networkAccount->id,
        ]);

        // Check if the API key is valid
        if (empty($password)) {
            Log::warning("Empty API key for Partner Ads network account ID: {$networkAccount->id}");

            return $url; // Return the URL without appending an empty key
        }

        // Remove any leading slashes that might have been added to the API key
        $password = ltrim($password, '/');

        // If the URL already ends with 'key=', just append the password
        if (substr($url, -4) === 'key=') {
            return $url.$password;
        }

        // If the URL already contains 'key=' somewhere (not at the end)
        if (strpos($url, 'key=') !== false) {
            // Replace the empty key with the actual password
            return str_replace('key=', 'key='.$password, $url);
        }

        // If the URL contains a query string but no 'key=' parameter
        if (strpos($url, '?') !== false) {
            return $url.'&key='.$password;
        } else {
            return $url.'?key='.$password;
        }
    }

    protected function parseMerchants(mixed $content, int $networkId): array
    {
        $timestamp = now()->format('Y-m-d H:i:s');

        try {
            $merchants = [];

            // Handle string response
            if (is_string($content)) {
                Log::info("[{$timestamp}] Received string response", [
                    'content_preview' => substr($content, 0, 500),
                    'content_full' => $content,
                ]);

                // If the response is XML, try to parse it
                if (strpos($content, '<?xml') !== false) {
                    Log::info("[{$timestamp}] Detected XML response, attempting to parse", [
                        'content_preview' => substr($content, 0, 500),
                    ]);

                    libxml_use_internal_errors(true);
                    $xml = simplexml_load_string($content);
                    $errors = libxml_get_errors();
                    libxml_clear_errors();

                    if ($xml === false) {
                        $errorMessages = array_map(
                            fn ($error) => sprintf('Line %d: %s', $error->line, trim($error->message)),
                            $errors
                        );

                        $errorString = implode('; ', $errorMessages);
                        Log::error("[{$timestamp}] XML parsing failed", [
                            'errors' => $errorString,
                            'content_preview' => substr($content, 0, 500),
                        ]);

                        throw new Exception('XML parsing failed: '.$errorString);
                    }

                    Log::info("[{$timestamp}] Successfully parsed XML", [
                        'root_element' => $xml->getName(),
                        'has_program' => isset($xml->program) ? 'yes' : 'no',
                        'has_partnerprogrammer' => isset($xml->partnerprogrammer) ? 'yes' : 'no',
                    ]);

                    // Check if the root element is 'partnerprogrammer' and it contains 'program' elements
                    if (isset($xml->partnerprogrammer) && isset($xml->partnerprogrammer->program)) {
                        Log::info("[{$timestamp}] Found partnerprogrammer->program structure", [
                            'program_count' => count($xml->partnerprogrammer->program),
                        ]);
                        // Use the 'program' elements inside 'partnerprogrammer'
                        $content = $xml->partnerprogrammer;
                    } else {
                        // Use the original XML structure
                        $content = $xml;
                    }
                } elseif (strpos($content, '{') === 0 || strpos($content, '[') === 0) {
                    // Try to parse as JSON
                    try {
                        $content = json_decode($content, true);
                        Log::info("[{$timestamp}] Successfully parsed JSON response");
                    } catch (Exception $e) {
                        Log::warning("[{$timestamp}] Failed to parse JSON response", [
                            'error' => $e->getMessage(),
                        ]);

                        return ['merchants' => []];
                    }
                } else {
                    // If not XML, return empty merchants array
                    Log::warning("[{$timestamp}] Received non-XML string response", [
                        'content' => $content,
                        'network_id' => $networkId,
                    ]);

                    return ['merchants' => []];
                }
            }

            // Process XML object
            if (is_object($content) && (isset($content->program) ||
                (isset($content->partnerprogrammer) && isset($content->partnerprogrammer->program)) ||
                (isset($content->partnerprogram) && isset($content->partnerprogram->program)))) {

                // Handle different XML structures
                if (isset($content->partnerprogrammer) && isset($content->partnerprogrammer->program)) {
                    // Danish format
                    $content = $content->partnerprogrammer;
                    $format = 'DK';
                } elseif (isset($content->partnerprogram) && isset($content->partnerprogram->program)) {
                    // Swedish format
                    $content = $content->partnerprogram;
                    $format = 'SE';
                } else {
                    // Default format
                    $format = $this->detectMerchantFormat($content);
                }

                Log::info("[{$timestamp}] Processing XML object with program data", [
                    'program_count' => count($content->program),
                    'format' => $format,
                ]);

                foreach ($content->program as $program) {
                    try {
                        // Map Partner Ads merchant format based on detected format
                        $merchant = $this->mapMerchantByFormat($program, $format);

                        // Add feed URLs if available
                        $feedField = $format === 'SE' ? 'feedlink' : 'feedlink';
                        if ($merchant['feed'] && isset($program->$feedField) && ! empty((string) $program->$feedField)) {
                            $merchant['feedUrls'] = [
                                ['feed_url' => (string) $program->$feedField],
                            ];
                        }

                        $merchants[] = $merchant;
                    } catch (Exception $e) {
                        Log::warning("[{$timestamp}] Failed to parse merchant", [
                            'program' => json_encode($program),
                            'error' => $e->getMessage(),
                        ]);
                    }
                }
            }
            // Process JSON array response
            elseif (is_array($content)) {
                Log::info("[{$timestamp}] Processing JSON array response", [
                    'content_keys' => is_array($content) ? array_keys($content) : 'not an array',
                ]);
                // Check if it's a JSON response with programs array
                if (isset($content['programs']) && is_array($content['programs'])) {
                    Log::info("[{$timestamp}] Found programs array in JSON response", [
                        'program_count' => count($content['programs']),
                    ]);
                    foreach ($content['programs'] as $program) {
                        try {
                            $merchant = [
                                'program_id' => (string) ($program['programid'] ?? ''),
                                'name' => (string) ($program['programnamn'] ?? ''),
                                'merchant_url' => (string) ($program['programurl'] ?? ''),
                                'logo_url' => '', // Partner Ads JSON doesn't seem to include logo URL
                                'description' => (string) ($program['programbeskrivning'] ?? ''),
                                'currency' => (string) ($program['currency'] ?? 'SEK'),
                                'feed' => isset($program['feed']) && $program['feed'] === 'ja',
                            ];

                            // Add feed URLs if available
                            if ($merchant['feed'] && isset($program['feedlink'])) {
                                $merchant['feedUrls'] = [
                                    ['feed_url' => (string) $program['feedlink']],
                                ];
                            }

                            $merchants[] = $merchant;
                        } catch (Exception $e) {
                            Log::warning("[{$timestamp}] Failed to parse merchant from JSON", [
                                'program' => json_encode($program),
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                }
                // Check if it's a direct array of programs
                elseif (! empty($content) && isset($content[0]) && is_array($content[0])) {
                    Log::info("[{$timestamp}] Found direct array of programs", [
                        'program_count' => count($content),
                    ]);
                    foreach ($content as $program) {
                        try {
                            $merchant = [
                                'program_id' => (string) ($program['programid'] ?? ''),
                                'name' => (string) ($program['programnamn'] ?? ''),
                                'merchant_url' => (string) ($program['programurl'] ?? ''),
                                'logo_url' => '', // Partner Ads JSON doesn't seem to include logo URL
                                'description' => (string) ($program['programbeskrivning'] ?? ''),
                                'currency' => (string) ($program['currency'] ?? 'SEK'),
                                'feed' => isset($program['feed']) && $program['feed'] === 'ja',
                            ];

                            // Add feed URLs if available
                            if ($merchant['feed'] && isset($program['feedlink'])) {
                                $merchant['feedUrls'] = [
                                    ['feed_url' => (string) $program['feedlink']],
                                ];
                            }

                            $merchants[] = $merchant;
                        } catch (Exception $e) {
                            Log::warning("[{$timestamp}] Failed to parse merchant from JSON array", [
                                'program' => json_encode($program),
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                }
                // Check if it's a program object with programid as keys
                elseif (! empty($content) && is_array($content) && isset(array_values($content)[0]['programid'])) {
                    Log::info("[{$timestamp}] Found program object with programid as keys", [
                        'program_count' => count($content),
                    ]);

                    foreach ($content as $program) {
                        try {
                            if (! isset($program['programid'])) {
                                continue;
                            }

                            $merchant = [
                                'program_id' => (string) ($program['programid'] ?? ''),
                                'name' => (string) ($program['programnavn'] ?? ''),
                                'merchant_url' => (string) ($program['programurl'] ?? ''),
                                'logo_url' => (string) ($program['logo'] ?? ''),
                                'description' => (string) ($program['programbeskrivelse'] ?? ''),
                                'currency' => (string) ($program['currency'] ?? 'DKK'),
                                'feed' => isset($program['feed']) && ($program['feed'] === 'ja' || $program['feed'] === 'yes'),
                            ];

                            // Add feed URLs if available
                            if ($merchant['feed'] && isset($program['feedlink'])) {
                                $merchant['feedUrls'] = [
                                    ['feed_url' => (string) $program['feedlink']],
                                ];
                            }

                            $merchants[] = $merchant;
                        } catch (Exception $e) {
                            Log::warning("[{$timestamp}] Failed to parse merchant from program object", [
                                'program' => json_encode($program),
                                'error' => $e->getMessage(),
                            ]);
                        }
                    }
                } else {
                    Log::warning("[{$timestamp}] Content is an array but does not contain expected program data", [
                        'content_keys' => is_array($content) ? array_keys($content) : 'not an array',
                        'network_id' => $networkId,
                    ]);
                }
            } else {
                Log::warning("[{$timestamp}] Content does not contain program data", [
                    'content_type' => gettype($content),
                    'network_id' => $networkId,
                ]);
            }

            Log::info("[{$timestamp}] Successfully parsed merchants", [
                'count' => count($merchants),
                'network_id' => $networkId,
            ]);

            return ['merchants' => $merchants];
        } catch (Exception $e) {
            Log::error("[{$timestamp}] Failed to parse merchants data", [
                'network_id' => $networkId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            throw $e;
        }
    }

    /**
     * Detect merchant format based on field names
     */
    private function detectMerchantFormat(object $content): string
    {
        // Check for Swedish format
        if (isset($content->program) && isset($content->program[0])) {
            $program = $content->program[0];
            if (isset($program->programnamn) || isset($program->programbeskrivning) || isset($program->rabattsidor)) {
                return 'SE';
            }

            // Check for Norwegian format
            if (isset($program->provisjon) || isset($program->klikksats) || isset($program->rabattsites)) {
                return 'NO';
            }
        }

        // Default to Danish format
        return 'DK';
    }

    /**
     * Map merchant data based on detected format
     */
    private function mapMerchantByFormat(object $program, string $format): array
    {
        switch ($format) {
            case 'SE':
                return [
                    'program_id' => (string) $program->programid,
                    'name' => (string) $program->programnamn,
                    'merchant_url' => (string) $program->programurl,
                    'logo_url' => '', // Partner Ads XML doesn't include logo URL
                    'description' => (string) ($program->programbeskrivning ?? ''),
                    'merchant_email' => (string) ($program->{'e-post'} ?? ''),
                    'merchant_contact' => (string) ($program->kontaktperson ?? ''),
                    'currency' => isset($program->feedcur) ? (string) $program->feedcur : 'SEK',
                    'click_rate' => (float) ($program->klicksats ?? 0),
                    'lead_rate' => (float) ($program->leadsats ?? 0),
                    'provision' => (float) ($program->provision ?? 0),
                    'epc' => (float) ($program->epc ?? 0),
                    'allow_sem_ppc' => $this->parseAllowedStatus((string) $program->SEM_PPC ?? ''),
                    'allow_social_ppc' => $this->parseAllowedStatus((string) $program->Social_PPC ?? ''),
                    'allow_shopping_ads' => $this->parseAllowedStatus((string) $program->ShoppingAds ?? ''),
                    'allow_cashback' => $this->parseAllowedStatus((string) $program->cashback ?? ''),
                    'allow_discount_sites' => $this->parseAllowedStatus((string) $program->rabattsidor ?? ''),
                    'feed' => $this->parseYesNoStatus((string) $program->feed ?? ''),
                ];

            case 'NO':
                return [
                    'program_id' => (string) $program->programid,
                    'name' => (string) $program->programnavn,
                    'merchant_url' => (string) $program->programurl,
                    'logo_url' => '', // Partner Ads XML doesn't include logo URL
                    'description' => (string) ($program->programbeskrivelse ?? ''),
                    'merchant_email' => (string) ($program->{'e-post'} ?? ''),
                    'merchant_contact' => (string) ($program->kontaktperson ?? ''),
                    'currency' => isset($program->feedcur) ? (string) $program->feedcur : 'NOK',
                    'click_rate' => (float) ($program->klikksats ?? 0),
                    'lead_rate' => (float) ($program->leadsats ?? 0),
                    'provision' => (float) ($program->provisjon ?? 0),
                    'epc' => (float) ($program->epc ?? 0),
                    'allow_sem_ppc' => $this->parseAllowedStatus((string) $program->SEM_PPC ?? ''),
                    'allow_social_ppc' => $this->parseAllowedStatus((string) $program->Social_PPC ?? ''),
                    'allow_shopping_ads' => $this->parseAllowedStatus((string) $program->ShoppingAds ?? ''),
                    'allow_cashback' => $this->parseAllowedStatus((string) $program->cashback ?? ''),
                    'allow_discount_sites' => $this->parseAllowedStatus((string) $program->rabattsites ?? ''),
                    'feed' => $this->parseYesNoStatus((string) $program->feed ?? ''),
                ];

            case 'DK':
            default:
                return [
                    'program_id' => (string) $program->programid,
                    'name' => (string) $program->programnavn,
                    'merchant_url' => (string) $program->programurl,
                    'logo_url' => '', // Partner Ads XML doesn't include logo URL
                    'description' => (string) ($program->programbeskrivelse ?? ''),
                    'merchant_email' => (string) ($program->email ?? ''),
                    'merchant_contact' => (string) ($program->kontaktperson ?? ''),
                    'currency' => isset($program->feedcur) ? (string) $program->feedcur : 'DKK',
                    'click_rate' => (float) ($program->kliksats ?? 0),
                    'lead_rate' => (float) ($program->leadsats ?? 0),
                    'provision' => (float) ($program->provision ?? 0),
                    'epc' => (float) ($program->epc ?? 0),
                    'allow_sem_ppc' => $this->parseAllowedStatus((string) $program->SEM_PPC ?? ''),
                    'allow_social_ppc' => $this->parseAllowedStatus((string) $program->Social_PPC ?? ''),
                    'allow_shopping_ads' => $this->parseAllowedStatus((string) $program->ShoppingAds ?? ''),
                    'allow_cashback' => $this->parseAllowedStatus((string) $program->cashback ?? ''),
                    'allow_discount_sites' => $this->parseAllowedStatus((string) $program->rabatsites ?? ''),
                    'feed' => $this->parseYesNoStatus((string) $program->feed ?? ''),
                ];
        }
    }

    /**
     * Parse allowed status from Partner Ads format
     */
    private function parseAllowedStatus(string $status): bool
    {
        if (empty($status)) {
            return false;
        }

        $status = strtolower($status);

        return $status === 'tilladt' || $status === 'tillåtet' || $status === 'tillatt';
    }

    /**
     * Parse yes/no status from Partner Ads format
     */
    private function parseYesNoStatus(string $status): bool
    {
        if (empty($status)) {
            return false;
        }

        $status = strtolower($status);

        return $status === 'ja' || $status === 'yes';
    }

    public function processProducts(object $merchant, string $feedUrl): int
    {
        $timestamp = now()->format('Y-m-d H:i:s');
        Log::info("[{$timestamp}] Starting product processing for merchant: {$merchant->name} from feed: {$feedUrl}");
        $processedCount = 0;

        try {
            $feedContent = $this->httpClient->get($feedUrl);

            if (empty($feedContent)) {
                Log::warning("[{$timestamp}] Empty feed content for merchant: {$merchant->name}, URL: {$feedUrl}");

                return 0;
            }

            // Assuming parseProducts takes the raw XML/content and returns a structured array
            $parsedFeedData = $this->parseProducts($feedContent);

            if (empty($parsedFeedData) || ! isset($parsedFeedData['products']) || ! is_array($parsedFeedData['products'])) {
                Log::warning("[{$timestamp}] No products found or invalid structure after parsing feed for merchant: {$merchant->name}, URL: {$feedUrl}");

                return 0;
            }

            $products = $parsedFeedData['products'];

            foreach ($products as $productData) {
                try {
                    // The mapProductByFormat might need the format detected earlier, or parseProducts handles it.
                    // Assuming $productData is already in a somewhat consistent format or mapProductByFormat can handle it.
                    // Or, we might need to detect product format here based on $productData or $feedUrl structure if not done by parseProducts.
                    // For now, let's assume parseProducts gives a uniform structure or mapProductByFormat is robust.

                    // We need to know the structure of $productData to map it correctly to MerchantProduct fields.
                    // Based on PartnerAds.md, fields like 'produktid', 'produktnavn', 'ean', 'nypris', etc. are expected.
                    // This mapping will be crucial and might need adjustment based on actual $productData structure.

                    MerchantProduct::updateOrCreate(
                        [
                            'merchant_id' => $merchant->id,
                            'unique_identifier' => $merchant->id.'-'.($productData['produktid'] ?? ($productData['id'] ?? uniqid())),
                        ],
                        [
                            'name' => (string) ($productData['produktnavn'] ?? ($productData['name'] ?? null)),
                            'ean' => (string) ($productData['ean'] ?? null),
                            'description' => (string) ($productData['beskrivelse'] ?? ($productData['description'] ?? null)),
                            'price' => (float) ($productData['nypris'] ?? ($productData['price'] ?? 0.00)),
                            'old_price' => (float) ($productData['glpris'] ?? ($productData['old_price'] ?? null)),
                            'direct_url' => (string) ($productData['vareurl'] ?? ($productData['url'] ?? null)),
                            'image_url' => (string) ($productData['billedurl'] ?? ($productData['image_url'] ?? null)),
                            'brand' => (string) ($productData['brand'] ?? null),
                            'category' => (string) ($productData['kategorinavn'] ?? ($productData['category'] ?? null)),
                        ]
                    );
                    $processedCount++;
                } catch (\Exception $e) {
                    Log::error("[{$timestamp}] Error processing a single product for merchant: {$merchant->name}", [
                        'product_data' => $productData,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            }

            Log::info("[{$timestamp}] Successfully processed {$processedCount} products for merchant: {$merchant->name} from {$feedUrl}");

        } catch (\GuzzleHttp\Exception\RequestException $e) {
            Log::error("[{$timestamp}] HTTP request failed for product feed: {$feedUrl} for merchant: {$merchant->name}", [
                'error' => $e->getMessage(),
                'response' => $e->hasResponse() ? (string) $e->getResponse()->getBody() : null,
            ]);
        } catch (\Exception $e) {
            Log::error("[{$timestamp}] Failed to process products for merchant: {$merchant->name} from {$feedUrl}", [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            // Optionally rethrow or handle more gracefully
        }

        return $processedCount;
    }

    protected function parseProducts(mixed $content): array
    {
        $timestamp = now()->format('Y-m-d H:i:s');
        Log::info("[{$timestamp}] Parsing products from feed content.");
        $products = [];
        $rawProducts = [];

        if (empty($content)) {
            Log::warning("[{$timestamp}] Product feed content is empty.");

            return ['products' => []];
        }

        try {
            // Enhanced UTF-8 conversion using our TextEncodingService
            if (is_string($content) && str_starts_with(trim($content), '<?xml')) {
                Log::info("[{$timestamp}] Processing XML content, checking encoding");

                if (! \App\Services\TextEncodingService::isValidUtf8($content)) {
                    Log::info("[{$timestamp}] Content is not valid UTF-8, attempting conversion");
                    $originalContent = $content;
                    $content = \App\Services\TextEncodingService::toUtf8($content);

                    if ($content !== $originalContent) {
                        Log::info("[{$timestamp}] Successfully converted content to UTF-8", [
                            'original_sample' => substr($originalContent, 0, 200),
                            'converted_sample' => substr($content, 0, 200),
                        ]);
                    }
                } else {
                    Log::info("[{$timestamp}] Content is already valid UTF-8");
                }
            }

            // Enable internal libxml error handling to capture detailed errors
            libxml_use_internal_errors(true);

            // SimpleXML parsing for PartnerAds XML structure
            // The root element can vary (e.g., <produkter>, <products>)
            // Each product is typically <produkt> or <product>
            $xml = @simplexml_load_string($content, 'SimpleXMLElement', LIBXML_NOCDATA);

            if ($xml === false) {
                Log::error("[{$timestamp}] Failed to parse product feed XML. Raw content: ".substr(strval($content), 0, 500));
                foreach (libxml_get_errors() as $error) {
                    Log::error("XML Error: {$error->message}");
                }
                libxml_clear_errors();

                return ['products' => []]; // Return empty if XML parsing fails
            }

            // Determine the product element name (e.g., 'produkt', 'product')
            $productElementName = null;
            if (isset($xml->produkt)) {
                $productElementName = 'produkt';
            } elseif (isset($xml->product)) {
                $productElementName = 'product';
            }
            // Add more possible root/product elements if needed

            if (! $productElementName) {
                Log::warning("[{$timestamp}] Could not determine product element name in feed.");

                return ['products' => []];
            }

            $detectedFormat = null; // We might need to detect format per product or assume one for the feed

            foreach ($xml->{$productElementName} as $item) {
                $productArray = json_decode(json_encode($item), true);

                // Try to detect format if not already done, or do it once per feed
                if ($detectedFormat === null) {
                    $detectedFormat = $this->detectProductFormat($productArray); // Assuming detectProductFormat can work with this array
                }

                if ($detectedFormat) {
                    $mappedProduct = $this->mapProductByFormat($productArray, $detectedFormat);
                    $products[] = $mappedProduct;
                } else {
                    // Fallback or generic mapping if format detection fails
                    $products[] = $productArray;
                }
                $rawProducts[] = $productArray; // For logging/debugging
            }

            if (empty($products)) {
                Log::info("[{$timestamp}] No products parsed from XML structure. Raw items count: ".count($xml->{$productElementName}));
                Log::debug("[{$timestamp}] Raw products array from XML: ", $rawProducts);
            }

        } catch (\Exception $e) {
            Log::error("[{$timestamp}] Exception during product parsing: ".$e->getMessage(), [
                'trace' => $e->getTraceAsString(),
            ]);

            return ['products' => []]; // Return empty on exception
        }

        Log::info("[{$timestamp}] Successfully parsed ".count($products).' products.');

        return ['products' => $products];
    }

    /**
     * Detect product format based on field names
     */
    private function detectProductFormat(array $product): string
    {
        // Swedish format detection
        if (isset($product['produktnamn']) || isset($product['nyttpris']) || isset($product['ordinariepris']) || isset($product['leveranstid'])) {
            return 'SE';
        }

        // Norwegian format detection (similar to Danish but with some differences)
        if (isset($product['produktnavn']) && isset($product['nypris']) && isset($product['glpris']) &&
            (isset($product['size']) || isset($product['color']) || isset($product['gender']))) {
            return 'NO';
        }

        // Default to Danish format
        return 'DK';
    }

    /**
     * Safely convert value to string, handling arrays and UTF-8 encoding
     */
    private function safeStringValue($value): string
    {
        if (is_array($value)) {
            $stringValue = is_array($value) && empty($value) ? '' : (string) (is_array($value) ? ($value[0] ?? '') : $value);
        } else {
            $stringValue = (string) ($value ?? '');
        }

        // Convert to proper UTF-8 encoding
        return \App\Services\TextEncodingService::cleanProductText($stringValue);
    }

    /**
     * Map product data based on detected format
     */
    private function mapProductByFormat(array $product, string $format): array
    {
        switch ($format) {
            case 'SE':
                return [
                    'produktid' => $this->safeStringValue($product['produktid'] ?? ''),
                    'produktnavn' => $this->safeStringValue($product['produktnamn'] ?? ''),
                    'beskrivelse' => $this->safeStringValue($product['beskrivning'] ?? ''),
                    'nypris' => (float) ($product['nyttpris'] ?? 0.00),
                    'glpris' => (float) ($product['ordinariepris'] ?? 0.00),
                    'currency' => 'SEK', // Default for Swedish Partner Ads
                    'vareurl' => $this->safeStringValue($product['produkturl'] ?? ''),
                    'billedurl' => $this->safeStringValue($product['bildurl'] ?? ''),
                    'kategorinavn' => $this->safeStringValue($product['kategorinamn'] ?? ''),
                    'brand' => $this->safeStringValue($product['brand'] ?? ''),
                    'ean' => $this->safeStringValue($product['ean'] ?? ''),
                    'sku' => '', // Not typically provided
                    'stock' => $this->parseStockStatus($product['lagerantal'] ?? ''),
                    'delivery_time' => (string) ($product['leveranstid'] ?? ''),
                    'shipping_cost' => (float) ($product['fraktomkostnader'] ?? 0.00),
                ];

            case 'NO':
                return [
                    'produktid' => $this->safeStringValue($product['produktid'] ?? ''),
                    'produktnavn' => $this->safeStringValue($product['produktnavn'] ?? ''),
                    'beskrivelse' => $this->safeStringValue($product['beskrivelse'] ?? ''),
                    'nypris' => (float) ($product['nypris'] ?? 0.00),
                    'glpris' => (float) ($product['glpris'] ?? 0.00),
                    'currency' => 'NOK', // Default for Norwegian Partner Ads
                    'vareurl' => $this->safeStringValue($product['vareurl'] ?? ''),
                    'billedurl' => $this->safeStringValue($product['billedurl'] ?? ''),
                    'kategorinavn' => $this->safeStringValue($product['kategorinavn'] ?? ''),
                    'brand' => $this->safeStringValue($product['brand'] ?? ''),
                    'ean' => $this->safeStringValue($product['ean'] ?? ''),
                    'sku' => '', // Not typically provided
                    'lagerantal' => $this->parseStockStatus($product['lagerantal'] ?? ''),
                    'delivery_time' => (string) ($product['leveringstid'] ?? ''),
                    'shipping_cost' => (float) ($product['fragtomk'] ?? 0.00),
                    // Additional Norwegian fields
                    'size' => (string) ($product['size'] ?? ''),
                    'color' => (string) ($product['color'] ?? ''),
                    'gender' => (string) ($product['gender'] ?? ''),
                ];

            case 'DK':
            default:
                return [
                    'produktid' => $this->safeStringValue($product['produktid'] ?? ''),
                    'produktnavn' => $this->safeStringValue($product['produktnavn'] ?? ''),
                    'beskrivelse' => $this->safeStringValue($product['beskrivelse'] ?? ''),
                    'nypris' => (float) ($product['nypris'] ?? 0.00),
                    'glpris' => (float) ($product['glpris'] ?? 0.00),
                    'currency' => 'DKK', // Default for Danish Partner Ads
                    'vareurl' => $this->safeStringValue($product['vareurl'] ?? ''),
                    'billedurl' => $this->safeStringValue($product['billedurl'] ?? ''),
                    'kategorinavn' => $this->safeStringValue($product['kategorinavn'] ?? ''),
                    'brand' => $this->safeStringValue($product['brand'] ?? ''),
                    'ean' => $this->safeStringValue($product['ean'] ?? ''),
                    'sku' => '', // Not typically provided
                    'lagerantal' => $this->parseStockStatus($this->safeStringValue($product['lagerantal'] ?? '')),
                    'leveringstid' => $this->safeStringValue($product['leveringstid'] ?? ''),
                    'fragtomk' => (float) ($product['fragtomk'] ?? 0.00),
                ];
        }
    }

    // No wait setup or retries

    /**
     * Parse stock status from Partner Ads format
     */
    private function parseStockStatus(string $status): int
    {
        if (empty($status)) {
            return 0;
        }

        $status = strtolower($status);
        if ($status === 'in_stock' || $status === 'instock' || $status === 'in stock') {
            return 1;
        }

        // Try to parse as numeric
        if (is_numeric($status) && (int) $status > 0) {
            return 1;
        }

        return 0;
    }

    protected function mapProgramToMerchant(array $program): array
    {
        $get = fn ($arr, $key, $default = null) => $arr[$key] ?? $default;

        return [
            'name' => $get($program, 'name'),
            'program_id' => (string) $get($program, 'programid'),
            'merchant_url' => $get($program, 'url'),
            'merchant_email' => $get($program, 'email'),
            'merchant_contact' => $get($program, 'contact'),
            'logo_url' => $get($program, 'logo'),
            'currency' => $get($program, 'currency', 'DKK'),
            'click_rate' => (float) $get($program, 'click_rate', 0),
            'lead_rate' => (float) $get($program, 'lead_rate', 0),
            'provision' => (float) $get($program, 'provision', 0),
            'epc' => (float) $get($program, 'epc', 0),
            'allow_sem_ppc' => (bool) $get($program, 'allow_ppc', false),
            'allow_social_ppc' => (bool) $get($program, 'allow_social', false),
            'allow_shopping_ads' => (bool) $get($program, 'allow_shopping', false),
            'feed' => (bool) ($get($program, 'feed') === 'yes'),
            'feedUrls' => [
                [
                    'feed_url' => $get($program, 'feed_url'),
                ],
            ],
        ];
    }
}
