<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;

/**
 * Service for handling text encoding conversions, specifically for fixing
 * UTF-8 encoding issues commonly found in product feeds and external data sources.
 */
class TextEncodingService
{
    /**
     * Convert text to proper UTF-8 encoding, handling common encoding issues.
     *
     * This method handles:
     * - ISO-8859-1 to UTF-8 conversion
     * - Double-encoded UTF-8 issues
     * - Windows-1252 encoding issues
     * - Common character replacements for Danish characters
     *
     * @param  string|null  $text  The text to convert
     * @return string The properly encoded UTF-8 text
     */
    public static function toUtf8(?string $text): string
    {
        if (empty($text)) {
            return '';
        }

        // First, try to detect and fix common encoding issues
        $text = self::fixCommonEncodingIssues($text);

        // Check if the text is already valid UTF-8
        if (mb_check_encoding($text, 'UTF-8')) {
            return $text;
        }

        // Try to convert from common encodings
        $encodings = ['ISO-8859-1', 'Windows-1252', 'CP1252'];

        foreach ($encodings as $encoding) {
            $converted = @mb_convert_encoding($text, 'UTF-8', $encoding);
            if ($converted !== false && mb_check_encoding($converted, 'UTF-8')) {
                Log::debug("Successfully converted text from {$encoding} to UTF-8", [
                    'original' => substr($text, 0, 100),
                    'converted' => substr($converted, 0, 100),
                ]);

                return $converted;
            }
        }

        // If all else fails, try to clean the string
        $cleaned = @mb_convert_encoding($text, 'UTF-8', 'UTF-8');
        if ($cleaned !== false) {
            return $cleaned;
        }

        // Last resort: remove non-UTF-8 characters
        return mb_convert_encoding($text, 'UTF-8', 'UTF-8');
    }

    /**
     * Fix common encoding issues found in Danish product feeds.
     *
     * @param  string  $text  The text to fix
     * @return string The fixed text
     */
    private static function fixCommonEncodingIssues(string $text): string
    {
        // Common Danish character encoding fixes
        $replacements = [
            // Danish characters that are commonly mis-encoded
            'Ã¸' => 'ø',  // ø
            'Ã¦' => 'æ',  // æ
            'Ã¥' => 'å',  // å
            'Ã˜' => 'Ø',  // Ø
            'Ã†' => 'Æ',  // Æ
            'Ã…' => 'Å',  // Å

            // Other common encoding issues
            'Ã©' => 'é',  // é
            'Ã¡' => 'á',  // á
            'Ã­' => 'í',  // í
            'Ã³' => 'ó',  // ó
            'Ãº' => 'ú',  // ú
            'Ã±' => 'ñ',  // ñ
            'Ã¼' => 'ü',  // ü
            'Ã¶' => 'ö',  // ö
            'Ã¤' => 'ä',  // ä

            // Common punctuation issues
            'â€™' => "'",  // Right single quotation mark
            'â€œ' => '"',  // Left double quotation mark
            'â€' => '"',   // Right double quotation mark
            'â€"' => '–',  // En dash
            'â€"' => '—',  // Em dash
            'â€¦' => '…',  // Horizontal ellipsis

            // Remove or replace problematic characters
            'Â' => '',     // Often appears as artifact
        ];

        return str_replace(array_keys($replacements), array_values($replacements), $text);
    }

    /**
     * Clean and normalize text for product data.
     *
     * @param  string|null  $text  The text to clean
     * @return string The cleaned text
     */
    public static function cleanProductText(?string $text): string
    {
        if (empty($text)) {
            return '';
        }

        // Convert to UTF-8 first
        $text = self::toUtf8($text);

        // Trim whitespace
        $text = trim($text);

        // Normalize whitespace (replace multiple spaces with single space)
        $text = preg_replace('/\s+/', ' ', $text);

        // Remove control characters except newlines and tabs
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        return $text;
    }

    /**
     * Validate that text is properly encoded UTF-8.
     *
     * @param  string  $text  The text to validate
     * @return bool True if the text is valid UTF-8
     */
    public static function isValidUtf8(string $text): bool
    {
        return mb_check_encoding($text, 'UTF-8');
    }

    /**
     * Get encoding information for debugging purposes.
     *
     * @param  string  $text  The text to analyze
     * @return array Encoding information
     */
    public static function getEncodingInfo(string $text): array
    {
        $encodings = ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'];
        $detected = [];

        foreach ($encodings as $encoding) {
            if (mb_check_encoding($text, $encoding)) {
                $detected[] = $encoding;
            }
        }

        return [
            'detected_encodings' => $detected,
            'is_utf8' => mb_check_encoding($text, 'UTF-8'),
            'length' => strlen($text),
            'mb_length' => mb_strlen($text, 'UTF-8'),
            'sample' => substr($text, 0, 100),
        ];
    }
}
