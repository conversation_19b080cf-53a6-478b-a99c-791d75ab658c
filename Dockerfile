# Production PHP + Nginx Unit Stage with Node.js
FROM unit:1.34.1-php8.3

# Install required packages and PHP extensions
RUN apt-get update && apt-get install -y \
    curl unzip git libicu-dev libzip-dev libpng-dev libjpeg-dev libfreetype6-dev libssl-dev \
    imagemagick libmagickwand-dev procps htop wget nano libxml2-dev libpq-dev \
    && docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) pcntl opcache pdo pdo_pgsql intl zip gd exif ftp bcmath simplexml \
    && pecl install redis imagick \
    && docker-php-ext-enable redis imagick

# Install Node.js directly
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs

# Configure PHP settings
RUN echo "opcache.enable=1" > /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.validate_timestamps=0" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.jit=tracing" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.jit_buffer_size=256M" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.memory_consumption=128" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.interned_strings_buffer=8" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "opcache.max_accelerated_files=4000" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "memory_limit=512M" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "upload_max_filesize=64M" >> /usr/local/etc/php/conf.d/custom.ini \
    && echo "post_max_size=64M" >> /usr/local/etc/php/conf.d/custom.ini

# Install Composer
COPY --from=composer:latest /usr/bin/composer /usr/local/bin/composer

# Copy healthcheck scripts
COPY --chown=root:root docker/healthcheck-* /usr/local/bin/
RUN chmod +x /usr/local/bin/healthcheck-*

# Clean up APT cache after installation to reduce image size
RUN apt-get autoremove -y && apt-get clean && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*

# Set working directory
WORKDIR /var/www/html

# Copy application code
COPY --chown=unit:unit . /var/www/html

# Set up Node.js build environment
WORKDIR /var/www/html

# Install Node.js dependencies and build assets
RUN npm ci --prefer-offline --no-audit --progress=false
RUN npm run build

# Install PHP dependencies with Composer (production mode)
RUN composer install --prefer-dist --optimize-autoloader --no-interaction --no-dev

# Create storage link
RUN php artisan storage:link

# Copy Unit configuration
COPY docker/unit.json /docker-entrypoint.d/unit.json

# Expose port
EXPOSE 8080

# Start Unit
CMD ["unitd", "--no-daemon"]
