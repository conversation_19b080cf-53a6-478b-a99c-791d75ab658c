# pulse

https://laravel.com/docs/11.x/pulse

- Url: http://localhost/pulse

# octane

https://laravel.com/docs/11.x/octane

- Do not use on local use on only Forge

# filamentphp

https://filamentphp.com/docs/3.x/
artisan make:filament-user

# Commands

## Development Commands

```bash
# Start the development server
php artisan serve

# Start the development server with Vite HMR
npm run dev

# Run the queue worker
php artisan queue:work

# Run the scheduler
php artisan schedule:work

# Run Inertia SSR server
npm run dev:ssr

# Start Inertia SSR
php artisan inertia:start-ssr
```

## Build Commands

```bash
# Build assets for production
npm run build

# Build Inertia SSR for production
npm run build:ssr
```

## Database Commands

```bash
# Run migrations
php artisan migrate

# Seed the database
php artisan db:seed

# Fresh migration with seeding
php artisan migrate:fresh --seed
```

## Maintenance Commands

```bash
# Clear application cache
php artisan cache:clear

# Clear route cache
php artisan route:clear

# Clear config cache
php artisan config:clear

# Clear view cache
php artisan view:clear

# Optimize the application
php artisan optimize

# Generate application key
php artisan key:generate
```

## Filament Commands

```bash
# Create a Filament user
php artisan make:filament-user

# Create a Filament resource
php artisan make:filament-resource ModelName
```

## Sync Commands

```bash
# Sync merchants data
php artisan command:merchants-sync

# Sync merchant reviews
php artisan command:merchants-reviews-sync

# Sync merchant logos
php artisan command:merchants-logos-sync

# Sync merchant products
php artisan command:merchants-products-sync

# Hard delete merchant products
php artisan command:merchants-products-hard-delete
```

# Deployment

This application supports two deployment methods: Nixpacks and Docker. Choose the one that best fits your infrastructure and requirements.

## Nixpacks Deployment

Nixpacks is a build system that automatically detects your application's dependencies and creates optimized container images. This project includes a `nixpacks.toml` configuration for seamless deployment on platforms like Coolify.

### Nixpacks Configuration

- Set `Build Pack` to `nixpacks`
- Set `APP_KEY` in your environment variables
- Set `Ports Exposes` to `80`
- Set `DB_CONNECTION`, `DB_HOST`, `DB_PORT`, `DB_DATABASE`, `DB_USERNAME`, and `DB_PASSWORD` for database connection
- Configure `MAIL_*` environment variables for email functionality

The Nixpacks configuration includes:
- PHP-FPM and Nginx setup
- Queue worker with supervisor
- Scheduler via cron
- Inertia SSR support
- Automatic asset compilation

### Deployment Steps

1. Push your code to your Git repository
2. Connect your repository to your Coolify instance
3. Configure the environment variables as mentioned above
4. Deploy the application

## Docker Deployment

This application includes a complete Docker setup with Nginx Unit for deployment. The setup includes:

- Main application container with Nginx Unit
- Laravel scheduler
- Queue worker
- Inertia SSR
- Horizon dashboard
- Pulse monitoring

### Docker Setup

To deploy using Docker:

1. Clone the repository to your server
2. Create a `.env` file based on `.env.example`
3. Run the following command:

```bash
docker-compose up -d
```

This will build and start all the services defined in the docker-compose.yaml file.

### Environment Configuration

Make sure to set the following environment variables in your `.env` file:

- `APP_KEY`: Your application encryption key
- `DB_CONNECTION`, `DB_HOST`, `DB_PORT`, `DB_DATABASE`, `DB_USERNAME`, `DB_PASSWORD`: Database connection details
- `REDIS_HOST`, `REDIS_PASSWORD`, `REDIS_PORT`: Redis connection details
- `MAIL_*`: Mail server configuration

### Post-deployment Commands

For both deployment methods, set the following as post-deployment commands:

```bash
php artisan optimize:clear && php artisan config:clear && php artisan route:clear && php artisan view:clear && php artisan optimize
```

These commands will clear all caches and then optimize the application for production.

### SSL Configuration

For SSL support:

1. For Nixpacks: Enable SSL in your Coolify settings
2. For Docker: Configure your reverse proxy (like Traefik or Nginx) to handle SSL termination

# DB Diagram

The DB Diagram can be changed in drawdb.vercel.app
![Database Diagram](./docs/db/aDNA-db-structure.svg)l

# PARSERS

To make a new parser, you create a Parser with the name from NetworkCompany + Parser, i.e. PartneradsDKParser.php
The code will automatically find the correct parser based on name.



<p align="center"><a href="https://laravel.com" target="_blank"><img src="https://raw.githubusercontent.com/laravel/art/master/logo-lockup/5%20SVG/2%20CMYK/1%20Full%20Color/laravel-logolockup-cmyk-red.svg" width="400" alt="Laravel Logo"></a></p>

<p align="center">
<a href="https://github.com/laravel/framework/actions"><img src="https://github.com/laravel/framework/workflows/tests/badge.svg" alt="Build Status"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/dt/laravel/framework" alt="Total Downloads"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/v/laravel/framework" alt="Latest Stable Version"></a>
<a href="https://packagist.org/packages/laravel/framework"><img src="https://img.shields.io/packagist/l/laravel/framework" alt="License"></a>
</p>

## About Laravel

Laravel is a web application framework with expressive, elegant syntax. We believe development must be an enjoyable and
creative experience to be truly fulfilling. Laravel takes the pain out of development by easing common tasks used in
many web projects, such as:

- [Simple, fast routing engine](https://laravel.com/docs/routing).
- [Powerful dependency injection container](https://laravel.com/docs/container).
- Multiple back-ends for [session](https://laravel.com/docs/session) and [cache](https://laravel.com/docs/cache)
  storage.
- Expressive, intuitive [database ORM](https://laravel.com/docs/eloquent).
- Database agnostic [schema migrations](https://laravel.com/docs/migrations).
- [Robust background job processing](https://laravel.com/docs/queues).
- [Real-time event broadcasting](https://laravel.com/docs/broadcasting).

Laravel is accessible, powerful, and provides tools required for large, robust applications.

## Learning Laravel

Laravel has the most extensive and thorough [documentation](https://laravel.com/docs) and video tutorial library of all
modern web application frameworks, making it a breeze to get started with the framework.

You may also try the [Laravel Bootcamp](https://bootcamp.laravel.com), where you will be guided through building a
modern Laravel application from scratch.

If you don't feel like reading, [Laracasts](https://laracasts.com) can help. Laracasts contains thousands of video
tutorials on a range of topics including Laravel, modern PHP, unit testing, and JavaScript. Boost your skills by digging
into our comprehensive video library.

## Laravel Sponsors

We would like to extend our thanks to the following sponsors for funding Laravel development. If you are interested in
becoming a sponsor, please visit the [Laravel Partners program](https://partners.laravel.com).

### Premium Partners

- **[Vehikl](https://vehikl.com/)**
- **[Tighten Co.](https://tighten.co)**
- **[WebReinvent](https://webreinvent.com/)**
- **[Kirschbaum Development Group](https://kirschbaumdevelopment.com)**
- **[64 Robots](https://64robots.com)**
- **[Curotec](https://www.curotec.com/services/technologies/laravel/)**
- **[Cyber-Duck](https://cyber-duck.co.uk)**
- **[DevSquad](https://devsquad.com/hire-laravel-developers)**
- **[Jump24](https://jump24.co.uk)**
- **[Redberry](https://redberry.international/laravel/)**
- **[Active Logic](https://activelogic.com)**
- **[byte5](https://byte5.de)**
- **[OP.GG](https://op.gg)**

## Contributing

Thank you for considering contributing to the Laravel framework! The contribution guide can be found in
the [Laravel documentation](https://laravel.com/docs/contributions).

## Code of Conduct

In order to ensure that the Laravel community is welcoming to all, please review and abide by
the [Code of Conduct](https://laravel.com/docs/contributions#code-of-conduct).

## Security Vulnerabilities

If you discover a security vulnerability within Laravel, please send an e-mail to Taylor Otwell
via [<EMAIL>](mailto:<EMAIL>). All security vulnerabilities will be promptly addressed.

## License

The Laravel framework is open-sourced software licensed under the [MIT license](https://opensource.org/licenses/MIT).
