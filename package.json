{"private": true, "type": "module", "scripts": {"build": "vite build", "build:ssr": "vite build && vite build --ssr", "dev": "vite", "format": "prettier --write resources/", "format:check": "prettier --check resources/", "lint": "eslint . --fix", "types": "tsc --noEmit"}, "devDependencies": {"@eslint/js": "^9.19.0", "@types/node": "^22.15.14", "@types/react-dom": "^19.1.3", "eslint": "^9.26.0", "eslint-config-prettier": "^10.1.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.5.3", "prettier-plugin-organize-imports": "^4.1.0", "prettier-plugin-tailwindcss": "^0.6.11", "typescript-eslint": "^8.32.0"}, "dependencies": {"@headlessui/react": "^2.2.2", "@inertiajs/react": "^2.0.8", "@tailwindcss/vite": "^4.1.5", "@types/react": "^19.1.3", "@vitejs/plugin-react": "^4.4.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "concurrently": "^9.1.2", "globals": "^15.15.0", "laravel-vite-plugin": "^1.2.0", "lucide-react": "^0.475.0", "react": "^19.1.0", "react-dom": "^19.1.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.0.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.8.3", "vite": "^6.3.5"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "4.9.5", "@tailwindcss/oxide-linux-x64-gnu": "^4.0.1", "lightningcss-linux-x64-gnu": "^1.29.3"}}