{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.3", "ext-simplexml": "*", "ext-libxml": "*", "ext-dom": "*", "ext-curl": "*", "filament/filament": "^3.2", "inertiajs/inertia-laravel": "^2.0", "laravel/framework": "^11.0", "laravel/octane": "^2.3", "laravel/pulse": "^1.2", "laravel/sanctum": "^4.0", "laravel/tinker": "^2.9", "tightenco/ziggy": "^2.5"}, "require-dev": {"barryvdh/laravel-debugbar": "3.13", "fakerphp/faker": "^1.23", "laravel/pint": "^1.21", "mockery/mockery": "^1.6", "nunomaduro/collision": "^8.0", "openai-php/laravel": "^0.9.0", "phpunit/phpunit": "^11.0.1", "roave/security-advisories": "dev-latest", "spatie/laravel-ignition": "^2.4"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}