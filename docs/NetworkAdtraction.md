# Adtracking in Services Networks

This document provides an overview of adtracking within services networks, focusing on how to integrate with various partner programs. It includes general concepts and specific examples using the Adtraction API.

## General Concepts

Adtracking in service networks involves monitoring and attributing user actions (like clicks, sign-ups, or purchases) to specific marketing channels or partners. This allows for:

*   **Performance Measurement:** Understanding which partners and campaigns are driving results.
*   **Commission Calculation:** Accurately compensating partners for referred conversions.
*   **Optimization:** Making data-driven decisions to improve marketing ROI.

Key components often include:

*   **Tracking Links:** Unique URLs provided to partners that contain identifiers to track clicks and subsequent actions.
*   **Cookies/Storage:** Mechanisms to store tracking information on a user's device.
*   **Server-to-Server (S2S) Tracking:** A more reliable method where conversion data is sent directly from the advertiser's server to the network's server.
*   **API Integrations:** Programmatic ways to retrieve program information, performance data, and manage campaigns.

## Typical Integration Steps

1.  **Registration/Approval:** Sign up with the ad network and get approved for specific partner programs.
2.  **Retrieve Program Information:** Use the network's API or dashboard to get details about available programs, including commission structures, allowed marketing methods, and tracking link parameters.
3.  **Implement Tracking:** Integrate tracking links into your promotional materials. For S2S tracking, set up server-side calls to report conversions.
4.  **Testing:** Thoroughly test the tracking setup to ensure clicks and conversions are recorded correctly.
5.  **Monitoring & Reporting:** Regularly check performance data and reports provided by the network.

## Adtraction API v3 Introduction

The Adtraction API (version 3) allows for programmatic access to Adtraction services. To use the API, you need an Adtraction account and a unique API token, which can be found in your Adtraction account under Account > Settings.

The base URL for all API version 3 endpoints is: `https://api.adtraction.net/v3/`

**Useful Links:**
*   API Status: [Check Adtraction API Status](https://status.adtraction.com/) (Assuming a link like this exists, replace if a more specific one is available)
*   Adtraction Home: [Adtraction.com](https://www.adtraction.com/)

If you have questions regarding the API, contact <EMAIL>.

### General Notes

*   **Encoding:** All strings passed to and from the Adtraction API must be UTF-8 encoded.
*   **SSL:** All requests must be made over SSL (HTTPS).
*   **Date Format:** The Adtraction tracking server operates on Stockholm time. All dates are in ISO 8601 format, expressed as UTC (Coordinated Universal Time). Time zones are represented as an offset from UTC. Example: `2019-07-16T14:45:15+0200`

### Header Parameters

Common header parameters for Adtraction API requests:

*   `X-Token`: Defines the request API token. This is used to determine privileges and visibility for the request.
*   `Content-Type`: Defines the format of data sent to Adtraction. Adtraction accepts JSON. Example: `application/json`
*   `Accept`: Defines the format of data expected from Adtraction. Adtraction supports JSON. Example: `application/json`

### Pagination

Certain API endpoints can generate large result sets. To manage this, these endpoints paginate results. Endpoints supporting pagination will return the following attributes:

*   `count`: The total number of results available.
*   `pageSize`: The number of results per page.
*   `page`: The page number being returned (the first page is 0).

### Rate Limiting

API endpoints are rate-limited by a quota per minute. Each request decreases the remaining quota. If the quota is exceeded, the API will return a `429 Too Many Requests` status code with a message indicating the time remaining until the quota resets.

Each API response includes the following header parameters regarding rate limits:

*   `X-RateLimit-Limit`: The maximum number of requests permitted per minute (or hour, as per original docs - the example showed per minute, the description per hour. Using 'window' for generality).
*   `X-RateLimit-Remaining`: The number of requests remaining in the current rate limit window.
*   `X-RateLimit-Reset`: The time (UTC epoch seconds or milliseconds, e.g., `1561967218095`) at which the current rate limit window resets.

Example Headers:
```
Status: 200 OK
X-RateLimit-Limit: 30
X-RateLimit-Remaining: 29
X-RateLimit-Reset: 1561967218095
```

### Response Codes and Statuses

Common HTTP response codes from the Adtraction API:

| Code | Status                  | Description                                                                         |
|------|-------------------------|-------------------------------------------------------------------------------------|
| 200  | OK                      | The request has succeeded.                                                          |
| 201  | Created                 | The request has been fulfilled and resulted in a new resource being created.        |
| 204  | No Content              | The server successfully processed the request, but is not returning any content.    |
| 400  | Bad Request             | The request could not be understood by the server due to malformed syntax.          |
| 401  | Unauthorized            | The request requires user authentication (e.g., missing or invalid `X-Token`).      |
| 403  | Forbidden               | The server understood the request, but the user is not authorized for the operation.|
| 404  | Not Found               | The server has not found anything matching the Request-URI.                         |
| 409  | Conflict                | The request could not be completed due to a conflict with the current resource state.|
| 415  | Unsupported Media Type  | The request entity has a media type which the server or resource does not support.  |
| 429  | Too Many Requests       | You are making too many requests and are being rate limited.                        |
| 500  | Internal Server Error   | The server encountered an unexpected condition preventing request fulfillment.        |

## Adtraction API Example: Retrieve Program Information

The Adtraction API allows partners to programmatically access information about programs they are approved for.

### Endpoint: `POST /v3/partner/programs/`

This endpoint retrieves detailed information about partner programs.

**URL:** `https://api.adtraction.com/v3/partner/programs/?token=YOUR_API_TOKEN`

**Method:** `POST`

**Parameters (in URL):**

*   `token` (string, required): Your Adtraction API token. Example: `E0E6BF3556DB0D83C8B401EBACBD6F1B0670633E`

**Request Headers:**

*   `Content-Type: application/json;charset=UTF-8`

**Request Body (JSON):**

```json
{
  "market": "SE"
}
```

**Request Body Attributes:**

*   `market` (string, required): Geographical market (ISO 3166-1 Alpha-2 country code). Example: `SE`
*   `programId` (number, optional): Numerical ID of a partner program. Example: `629059552`
*   `channelId` (number, optional): Numerical ID of a channel. Example: `1175982945`
*   `approvalStatus` (number, optional): Approval status (0 = rejected, 1 = approved, 2 = pending review). Example: `1`
*   `status` (number, optional): Program status on Adtraction (0 = Live, 3 = Closing). Example: `0`

**Example Response (200 OK):**

**Response Headers:**

*   `X-RateLimit-Limit: 30`
*   `X-RateLimit-Remaining: 29`
*   `X-RateLimit-Reset: 1565602012124` (Timestamp for when the rate limit resets)
*   `Content-Type: application/json;charset=UTF-8`

**Response Body (JSON):**

```json
[
  {
    "programId": 629059552,
    "market": "SE",
    "currency": "SEK",
    "approvalStatus": 1,
    "ppcMarketing": 1,
    "socialMarketing": 1,
    "emailMarketing": 1,
    "cashbackMarketing": 1,
    "couponMarketing": 1,
    "programName": "Kitchentime SE",
    "programURL": "https://www.kitchentime.se",
    "currentSegment": "Gold",
    "pendingActive": false,
    "cookieDuration": 45,
    "adId": 1062755777,
    "commissions": [
      {
        "id": "1066446216",
        "type": "SEK",
        "name": "Sale new customer",
        "value": 100,
        "transactionType": 3,
        "categories": [
          {
            "category": "Books",
            "type": "%",
            "value": 4.5
          }
        ],
        "thresholds": [
          {
            "orderValue": 1500,
            "type": "SEK",
            "value": 7.5
          }
        ]
      }
    ],
    "feeds": [
      {
        "feedUrl": "https://adtraction.com/productfeed.htm?type=feed&format=XML&encoding=UTF8&epi=0&zip=0&cdelim=tab&tdelim=singlequote&sd=0&flat=0&apid=1035156879&asid=141&pfid=506",
        "lastUpdated": "2022-02-03T02:05:06+0100",
        "numberOfProducts": 4564,
        "feedId": 759,
        "name": "Standard"
      }
    ],
    "logoURL": "https://secure.adtraction.com/image.htm?imgId=56807831",
    "trackingURL": "https://track.adtraction.com/t/t?a=24209996&as=24577691&t=2&tk=1",
    "categoryName": "Fashion & accessories",
    "categoryId": 1,
    "trackingType": 2,
    "status": 0,
    "epc": 1.94
  }
]
```

**Key Response Attributes:**

*   `programId`: Numerical ID of the partner program.
*   `market`: Geographical market.
*   `currency`: Currency used for the program.
*   `approvalStatus`: Your approval status for the program (if `channelId` was provided).
*   `ppcMarketing`, `socialMarketing`, `emailMarketing`, `cashbackMarketing`, `couponMarketing`: Rules for different marketing types (0 = not allowed, 1 = allowed, 2 = restricted).
*   `programName`: Name of the program.
*   `programURL`: Main URL for the program.
*   `cookieDuration`: Number of days tracking remains active after a click.
*   `adId`: Numerical ID of the ad to use in tracking links.
*   `commissions`: Array detailing commission structures (can include fixed amounts, percentages, categories, and thresholds).
*   `feeds`: Array of product feed URLs, if available.
*   `logoURL`: URL of the advertiser's logo.
*   `trackingURL`: Your default tracking link for the program (available if `channelId` provided and status is approved).
*   `categoryName`, `categoryId`: Adtraction platform category details.
*   `trackingType`: Type of tracking implemented by the advertiser (e.g., 1/5 = first-party cookie, 2 = server-to-server).
*   `status`: Current status of the program (0 = Live, 3 = Closing).
*   `epc`: Earnings Per Click, an indicator of program profitability.

## Best Practices

*   **Secure API Tokens:** Store API tokens securely and never expose them in client-side code.
*   **Error Handling:** Implement robust error handling for API requests, including checking HTTP status codes and response bodies for error messages.
*   **Rate Limiting:** Be mindful of API rate limits. The Adtraction API, for example, returns `X-RateLimit-Limit`, `X-RateLimit-Remaining`, and `X-RateLimit-Reset` headers. If you exceed the limit (e.g., receive a `429 Too Many Requests` error), wait until the reset time before making further requests.
*   **Data Caching:** Cache API responses where appropriate to reduce the number of calls and improve performance, respecting data freshness requirements.
*   **Parameter Usage:** Understand which API parameters are optional or required for your specific use case to retrieve the most relevant data efficiently.

## Troubleshooting

*   **Authentication Errors:** Double-check your API token and ensure it's correctly included in requests.
*   **Invalid Parameters:** Verify that all request parameters (both in the URL and body) are correctly formatted and that required parameters are included.
*   **`429 Too Many Requests`:** If you encounter this error, you have hit the API rate limit. Check the `X-RateLimit-Reset` header (provides a Unix timestamp in milliseconds) to know when you can resume making requests. It's crucial to implement a backoff strategy.
*   **Unexpected Response Data:** Ensure your request parameters are filtering the data as expected. Review the API documentation for the exact meaning of each field.

This document should serve as a starting point for integrating with adtracking services. Always refer to the specific API documentation provided by each network for the most up-to-date and detailed information.
